<?php
require_once __DIR__ . '/../includes/auth.php';
require_admin();
require_once __DIR__ . '/../includes/db.php';
require_once __DIR__ . '/../includes/functions.php';

// Approve/reject logic
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['id'], $_POST['action'])) {
    $id = (int)$_POST['id'];
    $action = $_POST['action'] === 'approve' ? 'Approved' : 'Rejected';

    // Get payment details
    $stmt = $pdo->prepare('SELECT * FROM payments WHERE id = ?');
    $stmt->execute([$id]);
    $payment = $stmt->fetch();

    if ($payment && $action === 'Approved') {
        // Update payment status
        $pdo->prepare('UPDATE payments SET status = ? WHERE id = ?')->execute([$action, $id]);

        // Upgrade user's subscription
        $subscription_plan = strtolower($payment['plan_name']); // 'basic' or 'pro'
        $expiry_date = date('Y-m-d', strtotime('+30 days')); // 30 days from now

        $pdo->prepare('UPDATE users SET subscription_plan = ?, subscription_expires = ?, usage_count = 0 WHERE id = ?')
            ->execute([$subscription_plan, $expiry_date, $payment['user_id']]);

        $success_message = "Payment approved and user account upgraded to {$payment['plan_name']} plan!";
    } else {
        // Just update payment status for rejection
        $pdo->prepare('UPDATE payments SET status = ? WHERE id = ?')->execute([$action, $id]);
        $success_message = $action === 'Approved' ? 'Payment approved!' : 'Payment rejected.';
    }
}

$transactions = $pdo->query('SELECT payments.*, users.name FROM payments JOIN users ON payments.user_id = users.id ORDER BY payments.timestamp DESC')->fetchAll();
?><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Payments | Health Oracle</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="/assets/css/style.css">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        .admin-card {
            border-radius: 1.5rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            background: #fff;
            border: none;
            transition: transform 0.3s ease;
        }
        .admin-card:hover {
            transform: translateY(-2px);
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 1rem;
            border: none;
        }
        .payment-card {
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
            border-radius: 1rem;
        }
        .payment-card:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .status-badge {
            font-size: 0.75rem;
            padding: 0.5rem 0.75rem;
            border-radius: 1rem;
        }
        .btn-action {
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }
        .btn-action:hover {
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
<?= getNavbarHtml() ?>
<div class="container py-5">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 mb-1" style="color:#495057;">
                        <i class="bi bi-credit-card-fill"></i> Payment Management
                    </h1>
                    <p class="text-muted mb-0">Review and approve user subscription payments</p>
                </div>
                <div class="stats-card p-3">
                    <div class="text-center">
                        <div class="h4 mb-0"><?= count(array_filter($transactions, fn($t) => $t['status'] === 'Pending')) ?></div>
                        <small>Pending Payments</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Success Message -->
    <?php if (isset($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle"></i> <?= htmlspecialchars($success_message) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Payments Table -->
    <div class="admin-card p-0">
        <div class="p-4 border-bottom">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-table"></i> All Payments</h5>
                <div class="d-flex gap-2">
                    <input type="text" class="form-control form-control-sm" placeholder="Search payments..." id="paymentSearch" style="width: 200px;">
                    <div class="dropdown">
                        <button class="btn btn-outline-primary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="bi bi-funnel"></i> Filter
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="filterPayments('all')">All Payments</a></li>
                            <li><a class="dropdown-item" href="#" onclick="filterPayments('Pending')">Pending Only</a></li>
                            <li><a class="dropdown-item" href="#" onclick="filterPayments('Approved')">Approved Only</a></li>
                            <li><a class="dropdown-item" href="#" onclick="filterPayments('Rejected')">Rejected Only</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-hover mb-0" id="paymentsTable">
                <thead class="bg-light">
                    <tr>
                        <th>User</th>
                        <th>Plan</th>
                        <th>Amount</th>
                        <th>Reference</th>
                        <th>Method</th>
                        <th>Status</th>
                        <th>Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($transactions as $t): ?>
                    <tr data-status="<?= $t['status'] ?>">
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px; font-size: 0.875rem;">
                                    <?= strtoupper(substr($t['name'], 0, 1)) ?>
                                </div>
                                <div class="fw-semibold"><?= htmlspecialchars($t['name']) ?></div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-info"><?= htmlspecialchars($t['plan_name']) ?></span>
                        </td>
                        <td>
                            <div class="fw-semibold text-success">$<?= htmlspecialchars($t['amount']) ?></div>
                        </td>
                        <td>
                            <code class="small"><?= htmlspecialchars($t['reference_code']) ?></code>
                        </td>
                        <td>
                            <span class="badge bg-secondary"><?= htmlspecialchars($t['payment_method']) ?></span>
                        </td>
                        <td>
                            <span class="status-badge bg-<?= $t['status'] === 'Approved' ? 'success' : ($t['status'] === 'Pending' ? 'warning' : 'danger') ?>">
                                <i class="bi bi-<?= $t['status'] === 'Approved' ? 'check-circle' : ($t['status'] === 'Pending' ? 'clock' : 'x-circle') ?>"></i>
                                <?= $t['status'] ?>
                            </span>
                        </td>
                        <td>
                            <div class="small">
                                <?= date('M d, Y', strtotime($t['timestamp'])) ?><br>
                                <span class="text-muted"><?= date('H:i', strtotime($t['timestamp'])) ?></span>
                            </div>
                        </td>
                        <td>
                            <?php if ($t['status'] === 'Pending'): ?>
                                <div class="d-flex gap-1">
                                    <form method="post" style="display: inline;">
                                        <input type="hidden" name="id" value="<?= $t['id'] ?>">
                                        <button name="action" value="approve" class="btn btn-success btn-action"
                                                onclick="return confirm('Approve this payment and upgrade user account?')"
                                                title="Approve Payment">
                                            <i class="bi bi-check"></i>
                                        </button>
                                    </form>
                                    <form method="post" style="display: inline;">
                                        <input type="hidden" name="id" value="<?= $t['id'] ?>">
                                        <button name="action" value="reject" class="btn btn-danger btn-action"
                                                onclick="return confirm('Reject this payment?')"
                                                title="Reject Payment">
                                            <i class="bi bi-x"></i>
                                        </button>
                                    </form>
                                </div>
                            <?php else: ?>
                                <span class="text-muted small">
                                    <i class="bi bi-check-circle"></i> Processed
                                </span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<script>
// Search functionality
document.getElementById('paymentSearch').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const tableRows = document.querySelectorAll('#paymentsTable tbody tr');

    tableRows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchTerm) ? '' : 'none';
    });
});

// Filter functionality
function filterPayments(status) {
    const tableRows = document.querySelectorAll('#paymentsTable tbody tr');

    tableRows.forEach(row => {
        if (status === 'all') {
            row.style.display = '';
        } else {
            const rowStatus = row.getAttribute('data-status');
            row.style.display = rowStatus === status ? '' : 'none';
        }
    });
}

// Auto-dismiss alerts after 5 seconds
setTimeout(() => {
    const alerts = document.querySelectorAll('.alert-dismissible');
    alerts.forEach(alert => {
        const bsAlert = new bootstrap.Alert(alert);
        bsAlert.close();
    });
}, 5000);
</script>
</body>
</html>

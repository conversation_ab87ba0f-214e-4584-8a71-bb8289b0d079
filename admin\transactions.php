<?php
require_once __DIR__ . '/../includes/auth.php';
require_admin();
require_once __DIR__ . '/../includes/db.php';
require_once __DIR__ . '/../includes/functions.php';

// Approve/reject logic
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['id'], $_POST['action'])) {
    $id = (int)$_POST['id'];
    $action = $_POST['action'] === 'approve' ? 'Approved' : 'Rejected';

    // Get payment details
    $stmt = $pdo->prepare('SELECT * FROM payments WHERE id = ?');
    $stmt->execute([$id]);
    $payment = $stmt->fetch();

    if ($payment && $action === 'Approved') {
        // Update payment status
        $pdo->prepare('UPDATE payments SET status = ? WHERE id = ?')->execute([$action, $id]);

        // Upgrade user's subscription
        $subscription_plan = strtolower($payment['plan_name']); // 'basic' or 'pro'
        $expiry_date = date('Y-m-d', strtotime('+30 days')); // 30 days from now

        $pdo->prepare('UPDATE users SET subscription_plan = ?, subscription_expires = ?, usage_count = 0 WHERE id = ?')
            ->execute([$subscription_plan, $expiry_date, $payment['user_id']]);

        $success_message = "Payment approved and user account upgraded to {$payment['plan_name']} plan!";
    } else {
        // Just update payment status for rejection
        $pdo->prepare('UPDATE payments SET status = ? WHERE id = ?')->execute([$action, $id]);
        $success_message = $action === 'Approved' ? 'Payment approved!' : 'Payment rejected.';
    }
}

$transactions = $pdo->query('SELECT payments.*, users.name FROM payments JOIN users ON payments.user_id = users.id ORDER BY payments.timestamp DESC')->fetchAll();
?><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Payments | Health Oracle</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&family=Inter:wght@400;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
    <style>
        body { font-family: 'Poppins', 'Inter', sans-serif; background: #F8F9FA; }
        .admin-card { border-radius: 1rem; box-shadow: 0 2px 16px rgba(70,130,180,0.08); background: #fff; }
    </style>
</head>
<body>
<?= getNavbarHtml() ?>
<div class="container py-5">
    <div class="admin-card p-4">
        <h2 class="mb-4" style="color:#007BFF;">All Payments</h2>

        <?php if (isset($success_message)): ?>
            <div class="alert alert-success"><?= htmlspecialchars($success_message) ?></div>
        <?php endif; ?>
        <div class="table-responsive">
        <table class="table table-bordered">
            <thead>
                <tr><th>User</th><th>Plan</th><th>Amount</th><th>Reference</th><th>Method</th><th>Status</th><th>Date</th><th>Action</th></tr>
            </thead>
            <tbody>
                <?php foreach ($transactions as $t): ?>
                <tr>
                    <td><?= htmlspecialchars($t['name']) ?></td>
                    <td><?= htmlspecialchars($t['plan_name']) ?></td>
                    <td><?= htmlspecialchars($t['amount']) ?></td>
                    <td><?= htmlspecialchars($t['reference_code']) ?></td>
                    <td><?= htmlspecialchars($t['payment_method']) ?></td>
                    <td><?= htmlspecialchars($t['status']) ?></td>
                    <td><?= htmlspecialchars($t['timestamp']) ?></td>
                    <td>
                        <?php if ($t['status'] === 'Pending'): ?>
                        <form method="post" class="d-flex gap-1">
                            <input type="hidden" name="id" value="<?= $t['id'] ?>">
                            <button name="action" value="approve" class="btn btn-success btn-sm">Approve</button>
                            <button name="action" value="reject" class="btn btn-danger btn-sm">Reject</button>
                        </form>
                        <?php else: ?>
                            <span class="text-muted">-</span>
                        <?php endif; ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        </div>
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

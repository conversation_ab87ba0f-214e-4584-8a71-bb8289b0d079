-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Aug 05, 2025 at 02:04 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `health-oracle`
--

-- --------------------------------------------------------

--
-- Table structure for table `announcements`
--

CREATE TABLE `announcements` (
  `id` int(11) NOT NULL,
  `message` text NOT NULL,
  `timestamp` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `diagnoses`
--

CREATE TABLE `diagnoses` (
  `id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `guest_ip` varchar(45) DEFAULT NULL,
  `input_text` text NOT NULL,
  `ai_response` text NOT NULL,
  `timestamp` datetime DEFAULT current_timestamp(),
  `text_summary` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `diagnoses`
--

INSERT INTO `diagnoses` (`id`, `user_id`, `guest_ip`, `input_text`, `ai_response`, `timestamp`, `text_summary`) VALUES
(1, NULL, '::1', 'I&#039;ve had fever for 3 days', 'Based on your symptoms, here are some possible considerations:\n\n**General recommendations:**\n• Monitor your symptoms\n• Rest and stay hydrated\n• Track any changes\n\n**⚠️ Important:** This is a general assessment only. Please consult with a healthcare professional for proper diagnosis and treatment, especially if symptoms worsen or persist.', '2025-08-05 10:35:15', 'Based on your symptoms, here are some possible considerations: **General recommendations:** • Monitor your symptoms • Rest and stay hydrated • Track any changes **⚠️ Important:** This is a g...'),
(6, NULL, '::1', 'I&#039;ve had sore throat for two days and now I have headache', '\n    <div class=\"analysis-results\">\n        <h4><i class=\"fas fa-stethoscope\"></i> Analysis Results</h4>\n\n        <div class=\"result-section mb-4\">\n            <div class=\"condition-card p-3 border-start border-success border-4\">\n                <h5><i class=\"fas fa-diagnoses\"></i> Possible Condition</h5>\n                <p class=\"mb-0 text-muted\">Tension Headache or Migraine</p>\n            </div>\n        </div>\n\n        <div class=\"result-section mb-4\">\n            <div class=\"urgency-card p-3\">\n                <h5><i class=\"fas fa-exclamation-triangle\"></i> Urgency Level</h5>\n                <span class=\"badge bg-success fs-6\">MILD</span>\n                <p class=\"mt-2 mb-0 small text-muted\">Monitor symptoms and rest</p>\n            </div>\n        </div>\n\n        <div class=\"result-section mb-4\">\n            <div class=\"recommendations-card p-3\">\n                <h5><i class=\"fas fa-clipboard-list\"></i> Recommendations</h5>\n                <p class=\"mb-0 text-muted\">Consult with a healthcare professional</p>\n            </div>\n        </div>\n\n        <div class=\"result-section\">\n            <div class=\"note-card p-3 bg-light\">\n                <h5><i class=\"fas fa-info-circle\"></i> Important Note</h5>\n                <p class=\"mb-0 small text-muted\">This analysis is for informational purposes only and should not replace professional medical advice. If you\'re experiencing severe symptoms or are concerned about your health, please consult a healthcare professional immediately.</p>\n            </div>\n        </div>\n    </div>\n\n    <style>\n    .analysis-results {\n        font-family: \"Segoe UI\", Tahoma, Geneva, Verdana, sans-serif;\n    }\n    .result-section h5 {\n        color: #2c3e50;\n        font-weight: 600;\n        margin-bottom: 0.5rem;\n    }\n    .condition-card, .urgency-card, .recommendations-card {\n        background: #f8f9fa;\n        border-radius: 8px;\n    }\n    .note-card {\n        border-radius: 8px;\n        border: 1px solid #dee2e6;\n    }\n    .fas {\n        margin-right: 8px;\n        color: #007bff;\n    }\n    </style>', '2025-08-05 11:05:44', 'Analysis Results Possible Condition Tension Headache or Migraine Urgency Level MILD Monitor symptoms and rest Recommendations Consult with a healthcare professional Important Note This analysis is for...'),
(7, NULL, '::1', 'I&#039;ve had sore throat for two days and now I have headache', '\n    <div class=\"analysis-results\">\n        <h4><i class=\"fas fa-stethoscope\"></i> Analysis Results</h4>\n\n        <div class=\"result-section mb-4\">\n            <div class=\"condition-card p-3 border-start border-success border-4\">\n                <h5><i class=\"fas fa-diagnoses\"></i> Possible Condition</h5>\n                <p class=\"mb-0 text-muted\">Tension Headache</p>\n            </div>\n        </div>\n\n        <div class=\"result-section mb-4\">\n            <div class=\"urgency-card p-3\">\n                <h5><i class=\"fas fa-exclamation-triangle\"></i> Urgency Level</h5>\n                <span class=\"badge bg-success fs-6\">MILD</span>\n                <p class=\"mt-2 mb-0 small text-muted\">Monitor symptoms and rest</p>\n            </div>\n        </div>\n\n        <div class=\"result-section mb-4\">\n            <div class=\"recommendations-card p-3\">\n                <h5><i class=\"fas fa-clipboard-list\"></i> Recommendations</h5>\n                <p class=\"mb-0 text-muted\">Rest, stay hydrated, manage stress. Over-the-counter pain relievers may help. Consult doctor if headaches are frequent or severe.</p>\n            </div>\n        </div>\n\n        <div class=\"result-section\">\n            <div class=\"note-card p-3 bg-light\">\n                <h5><i class=\"fas fa-info-circle\"></i> Important Note</h5>\n                <p class=\"mb-0 small text-muted\">This analysis is for informational purposes only and should not replace professional medical advice. If you\'re experiencing severe symptoms or are concerned about your health, please consult a healthcare professional immediately.</p>\n            </div>\n        </div>\n    </div>\n\n    <style>\n    .analysis-results {\n        font-family: \"Segoe UI\", Tahoma, Geneva, Verdana, sans-serif;\n    }\n    .result-section h5 {\n        color: #2c3e50;\n        font-weight: 600;\n        margin-bottom: 0.5rem;\n    }\n    .condition-card, .urgency-card, .recommendations-card {\n        background: #f8f9fa;\n        border-radius: 8px;\n    }\n    .note-card {\n        border-radius: 8px;\n        border: 1px solid #dee2e6;\n    }\n    .fas {\n        margin-right: 8px;\n        color: #007bff;\n    }\n    </style>', '2025-08-05 11:09:17', 'Analysis Results Possible Condition Tension Headache Urgency Level MILD Monitor symptoms and rest Recommendations Rest, stay hydrated, manage stress. Over-the-counter pain relievers may help. Consult ...');

-- --------------------------------------------------------

--
-- Table structure for table `guests`
--

CREATE TABLE `guests` (
  `id` int(11) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `usage_count` int(11) DEFAULT 0,
  `last_usage_date` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `guests`
--

INSERT INTO `guests` (`id`, `ip_address`, `usage_count`, `last_usage_date`) VALUES
(1, '::1', 3, '2025-08-05');

-- --------------------------------------------------------

--
-- Table structure for table `payments`
--

CREATE TABLE `payments` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `plan_name` varchar(100) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `reference_code` varchar(100) NOT NULL,
  `payment_method` varchar(50) NOT NULL,
  `status` enum('Pending','Approved','Rejected') DEFAULT 'Pending',
  `timestamp` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `payments`
--

INSERT INTO `payments` (`id`, `user_id`, `plan_name`, `amount`, `reference_code`, `payment_method`, `status`, `timestamp`) VALUES
(1, 1, 'Pro', 25.00, '23456789olknbgt', 'MTN', 'Approved', '2025-08-05 10:39:31'),
(2, 1, 'Pro', 25.00, 'ouwr&#039;sonb', 'MTN', 'Approved', '2025-08-05 11:17:03');

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `id` int(11) NOT NULL,
  `guest_limit` int(11) DEFAULT 3,
  `user_limit` int(11) DEFAULT 10
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`id`, `guest_limit`, `user_limit`) VALUES
(1, 3, 10);

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `email` varchar(150) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','user') DEFAULT 'user',
  `usage_count` int(11) DEFAULT 0,
  `last_usage_date` date DEFAULT NULL,
  `date_registered` datetime DEFAULT current_timestamp(),
  `subscription_plan` varchar(50) DEFAULT 'free',
  `subscription_expires` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `name`, `email`, `password`, `role`, `usage_count`, `last_usage_date`, `date_registered`, `subscription_plan`, `subscription_expires`) VALUES
(1, 'Elvis Brown', '<EMAIL>', '$2y$10$Vbq1FByXg1bijz8VpTWcD.N6JTnA4fi3CJMvu4Sj8VGKgcx/JSunK', 'user', 4, '2025-08-05', '2025-08-05 03:50:53', 'pro', '2025-09-04'),
(2, 'Admin', 'group9@localhost', '$2y$10$qJEVaSzSuanewxC2yg5JWeXNzNuxzQ7oEJT6Djy8S/rJ3ANa6ClcK', 'admin', 0, NULL, '2025-08-05 03:58:04', 'free', NULL),
(3, 'Admin', '<EMAIL>', '$2y$10$KW2LJCqA5zVvAsKo7neSze/D7GwOxU/ID4x3lipcbTAUeWkv6Vxs2', 'admin', 0, NULL, '2025-08-05 04:15:02', 'free', NULL);

--
-- Indexes for dumped tables
--

--
-- Indexes for table `announcements`
--
ALTER TABLE `announcements`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `diagnoses`
--
ALTER TABLE `diagnoses`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `guests`
--
ALTER TABLE `guests`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `payments`
--
ALTER TABLE `payments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `announcements`
--
ALTER TABLE `announcements`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `diagnoses`
--
ALTER TABLE `diagnoses`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `guests`
--
ALTER TABLE `guests`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `payments`
--
ALTER TABLE `payments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `diagnoses`
--
ALTER TABLE `diagnoses`
  ADD CONSTRAINT `diagnoses_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);

--
-- Constraints for table `payments`
--
ALTER TABLE `payments`
  ADD CONSTRAINT `payments_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;

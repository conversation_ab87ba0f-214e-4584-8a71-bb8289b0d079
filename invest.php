<?php
require_once __DIR__ . '/includes/auth.php';
require_login();
require_once __DIR__ . '/includes/db.php';
require_once __DIR__ . '/includes/functions.php';

$user_id = $_SESSION['user_id'];
$error = '';
$success = '';

// Example plans (could be loaded from DB in future)
$plans = [
    ['name' => 'Basic', 'amount' => 10, 'benefits' => '10 diagnoses/day'],
    ['name' => 'Pro', 'amount' => 25, 'benefits' => 'Unlimited diagnoses, priority support'],
];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $plan = sanitize_input($_POST['plan'] ?? '');
    $reference = sanitize_input($_POST['reference'] ?? '');
    $method = sanitize_input($_POST['method'] ?? '');
    $amount = 0;
    foreach ($plans as $p) {
        if ($p['name'] === $plan) $amount = $p['amount'];
    }
    if (!$plan || !$reference || !$method || !$amount) {
        $error = 'All fields are required.';
    } else {
        $stmt = $pdo->prepare('INSERT INTO payments (user_id, plan_name, amount, reference_code, payment_method, status) VALUES (?, ?, ?, ?, ?, ?)');
        $stmt->execute([$user_id, $plan, $amount, $reference, $method, 'Pending']);
        $success = 'Your payment has been submitted and is pending approval.';
    }
}
?><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invest - Health Oracle</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&family=Inter:wght@400;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
    <style>
        body { font-family: 'Poppins', 'Inter', sans-serif; background: #F8F9FA; }
        .invest-card { max-width: 500px; margin: 3rem auto; border-radius: 1rem; box-shadow: 0 2px 16px rgba(70,130,180,0.08); background: #fff; }
        .btn-primary { background: #007BFF; border: none; }
        .btn-primary:hover { background: #1E90FF; }
    </style>
</head>
<body>
<?= getNavbarHtml() ?>

<div class="container">
    <div class="mt-3"><?= renderConfigBanner() ?></div>
    <div class="invest-card p-4 mt-4">
        <h2 class="mb-4 text-center text-primary">Invest in Your Health</h2>
        <?php if ($error): ?>
            <div class="alert alert-danger"> <?= htmlspecialchars($error) ?> </div>
        <?php elseif ($success): ?>
            <div class="alert alert-success"> <?= htmlspecialchars($success) ?> </div>
        <?php endif; ?>
        <div class="alert alert-info mb-3">
            <strong>Send payment to: <span style="font-size:1.2em;color:#007BFF;">**********</span></strong>
        </div>
        <form method="post" autocomplete="off">
            <div class="mb-3">
                <label for="plan" class="form-label">Select Plan</label>
                <select class="form-select" id="plan" name="plan" required>
                    <option value="">Choose...</option>
                    <?php foreach ($plans as $p): ?>
                        <option value="<?= $p['name'] ?>"> <?= $p['name'] ?> (<?= $p['amount'] ?> USD) - <?= $p['benefits'] ?> </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="mb-3">
                <label for="reference" class="form-label">Transaction Reference</label>
                <input type="text" class="form-control" id="reference" name="reference" required>
            </div>
            <div class="mb-3">
                <label for="method" class="form-label">Payment Method</label>
                <select class="form-select" id="method" name="method" required>
                    <option value="">Choose...</option>
                    <option value="MTN">MTN</option>
                    <option value="Vodafone">Vodafone</option>
                    <option value="AirtelTigo">AirtelTigo</option>
                    <option value="Bank">Bank</option>
                </select>
            </div>
            <button type="submit" class="btn btn-primary w-100">Submit Payment</button>
        </form>
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

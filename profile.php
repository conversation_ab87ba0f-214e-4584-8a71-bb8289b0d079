<?php
require_once 'includes/auth.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

if (!is_logged_in()) {
    header('Location: /health-oracle/login.php');
    exit();
}

$user_id = $_SESSION['user_id'];
$success = '';
$error = '';

// Example plans (could be loaded from DB in future)
$plans = [
    ['name' => 'Basic', 'amount' => 10, 'benefits' => '50 diagnoses/day, Email support'],
    ['name' => 'Pro', 'amount' => 25, 'benefits' => 'Unlimited diagnoses, Priority support, Advanced features'],
];

// Handle payment submission
if ($_POST && isset($_POST['submit_payment'])) {
    $plan = sanitize_input($_POST['plan'] ?? '');
    $reference = sanitize_input($_POST['reference'] ?? '');
    $method = sanitize_input($_POST['method'] ?? '');
    $amount = 0;
    foreach ($plans as $p) {
        if ($p['name'] === $plan) $amount = $p['amount'];
    }
    if (!$plan || !$reference || !$method || !$amount) {
        $error = 'All payment fields are required.';
    } else {
        $stmt = $pdo->prepare('INSERT INTO payments (user_id, plan_name, amount, reference_code, payment_method, status) VALUES (?, ?, ?, ?, ?, ?)');
        $stmt->execute([$user_id, $plan, $amount, $reference, $method, 'Pending']);
        $success = 'Your payment has been submitted and is pending approval. You will be notified once processed.';
    }
}

// Handle profile update
if ($_POST && isset($_POST['update_profile'])) {
    $name = trim($_POST['name']);
    $email = trim($_POST['email']);
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];

    if (empty($name) || empty($email)) {
        $error = 'Name and email are required.';
    } else {
        // Get current user data
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$user_id]);
        $user = $stmt->fetch();

        // Check if email is already taken by another user
        $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
        $stmt->execute([$email, $user_id]);
        if ($stmt->fetch()) {
            $error = 'Email is already taken by another user.';
        } else {
            // Update basic info
            $stmt = $pdo->prepare("UPDATE users SET name = ?, email = ? WHERE id = ?");
            $stmt->execute([$name, $email, $user_id]);

            // Update password if provided
            if (!empty($new_password)) {
                if (!password_verify($current_password, $user['password'])) {
                    $error = 'Current password is incorrect.';
                } elseif ($new_password !== $confirm_password) {
                    $error = 'New passwords do not match.';
                } elseif (strlen($new_password) < 6) {
                    $error = 'New password must be at least 6 characters.';
                } else {
                    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                    $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
                    $stmt->execute([$hashed_password, $user_id]);
                    $success = 'Profile and password updated successfully!';
                }
            } else {
                $success = 'Profile updated successfully!';
            }

            // Update session data
            $_SESSION['name'] = $name;
        }
    }
}

// Get current user data
$stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$user_id]);
$user = $stmt->fetch();

// Check subscription status
$subscription_status = 'Free';
$subscription_expires = null;
if ($user['subscription_plan'] && $user['subscription_plan'] !== 'free') {
    if ($user['subscription_expires'] && $user['subscription_expires'] >= date('Y-m-d')) {
        $subscription_status = ucfirst($user['subscription_plan']) . ' (Active)';
        $subscription_expires = $user['subscription_expires'];
    } else {
        $subscription_status = ucfirst($user['subscription_plan']) . ' (Expired)';
    }
}

// Get user statistics
$stmt = $pdo->prepare("SELECT COUNT(*) as total_diagnoses FROM diagnoses WHERE user_id = ?");
$stmt->execute([$user_id]);
$stats = $stmt->fetch();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile - Health Oracle</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
    <style>
        body { font-family: 'Poppins', 'Inter', sans-serif; background: #F8F9FA; }
        .profile-card { border-radius: 1rem; box-shadow: 0 2px 16px rgba(70,130,180,0.08); background: #fff; }
        .btn-primary { background: #007BFF; border: none; }
        .btn-primary:hover { background: #1E90FF; }
        .stats-card { background: linear-gradient(135deg, #ADD8E6 0%, #87CEFA 100%); color: #003366; border-radius: 1rem; height:100% }
        .stats-card h5 { color: #003366; font-weight: 600; }
        .badge.bg-success { background-color: #28a745 !important; }
        .table th, .table td { vertical-align: middle; }
        .form-label { font-weight: 500; }
        .alert { border-radius: 0.75rem; }

        @media screen and (max-width: 768px) {
            .exp{ 
               display: flex;
            }
        }
    </style>

</head>
<body>
<?= getNavbarHtml() ?>

<div class="container py-5">
    <div class="row">
        <div class="col-md-4">
            <!-- User Stats Card -->
            <div class="stats-card p-4 mb-4">
                <h5 class="mb-3">📊 Your Statistics</h5>
                <div class="mb-2">
                    <strong>Total Diagnoses:</strong> <?= $stats['total_diagnoses'] ?>
                </div>
                <div class="mb-2">
                    <strong>Account Type:</strong> <?= ucfirst($user['role']) ?>
                </div>
                <div class="mb-2">
                    <strong>Subscription:</strong> <?= $subscription_status ?>
                </div>
                

                </div>
                <?php if ($subscription_expires): ?>
                <div class="exp mb-2" style="position: absolute; top: 32%; left: 17.3%; color: #003366;">
                    <strong>Expires:</strong> <?= date('M d, Y', strtotime($subscription_expires)) ?>
                </div>

                <?php endif; ?>
                <div class="exp mb-2" style="position: absolute; top: 29%; left: 17.3%; color: #003366;">
                    <strong>Member Since:</strong> <?= date('M Y', strtotime($user['date_registered'])) ?>
                </div>
                <?= renderConfigBanner() ?>
            </div>

            <div class="col-md-8">
            <!-- Profile Form -->
            <div class="profile-card p-4">
                <h3 class="mb-4">👤 Profile Settings</h3>

                <?php if ($success): ?>
                    <div class="alert alert-success"><?= htmlspecialchars($success) ?></div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-danger"><?= htmlspecialchars($error) ?></div>
                <?php endif; ?>

                <form method="POST">
                    <input type="hidden" name="csrf_token" value="<?= $_SESSION['csrf_token'] ?>">

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">Full Name</label>
                            <input type="text" class="form-control" id="name" name="name"
                                   value="<?= htmlspecialchars($user['name']) ?>" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="email" name="email"
                                   value="<?= htmlspecialchars($user['email']) ?>" required>
                        </div>
                    </div>

                    <hr class="my-4">
                    <h5>🔒 Change Password (Optional)</h5>
                    <p class="text-muted small">Leave blank to keep current password</p>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="current_password" class="form-label">Current Password</label>
                            <input type="password" class="form-control" id="current_password" name="current_password">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="new_password" class="form-label">New Password</label>
                            <input type="password" class="form-control" id="new_password" name="new_password">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="confirm_password" class="form-label">Confirm New Password</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="<?= is_admin() ? '/health-oracle/admin/dashboard.php' : '/health-oracle/dashboard.php' ?>"
                           class="btn btn-secondary">← Back to Dashboard</a>
                        <button type="submit" name="update_profile" class="btn btn-primary">Update Profile</button>
                    </div>
                </form>
            </div>
        </div>

            <!-- Subscription Card -->
            <div class="profile-card p-4 mt-4 mb-4">
                <h5 class="mb-3">💳 Subscription</h5>
                <?php if (empty($user['subscription_plan']) || $user['subscription_plan'] === 'free'): ?>
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <p class="mb-1 text-muted">You are currently on the Free plan.</p>
                            <small class="text-muted">Limited to 5 diagnoses per day</small>
                        </div>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#upgradeModal">
                            <i class="bi bi-arrow-up-circle"></i> Upgrade to Pro
                        </button>
                    </div>
                <?php else: ?>
                    <div class="d-flex align-items-center gap-2">
                        <span class="badge bg-success fs-6"><i class="bi bi-check-circle"></i> <?= ucfirst($user['subscription_plan']) ?> Plan</span>
                        <?php if ($subscription_expires): ?>
                            <span class="small text-muted">Valid until <?= date('M d, Y', strtotime($subscription_expires)) ?></span>
                        <?php endif; ?>
                    </div>
                    <div class="mt-2">
                        <small class="text-success"><i class="bi bi-infinity"></i> Unlimited diagnoses</small>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Recent Transactions -->
            <?php
            $tx = $pdo->prepare('SELECT plan_name, amount, reference_code, status, timestamp FROM payments WHERE user_id = ? ORDER BY timestamp DESC LIMIT 5');
            $tx->execute([$user_id]);
            $recent = $tx->fetchAll();
            ?>
            <div class="profile-card p-4 mb-4">
                <h5 class="mb-3">💰 Recent Transactions</h5>
                <?php if ($recent): ?>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr><th>Plan</th><th>Amount</th><th>Reference</th><th>Status</th><th>Date</th></tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent as $r): ?>
                            <tr>
                                <td><?= htmlspecialchars($r['plan_name']) ?></td>
                                <td><?= htmlspecialchars($r['amount']) ?></td>
                                <td><?= htmlspecialchars($r['reference_code']) ?></td>
                                <td><?= htmlspecialchars($r['status']) ?></td>
                                <td><?= htmlspecialchars($r['timestamp']) ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                    <p class="text-muted mb-0">No transactions yet.</p>
                <?php endif; ?>
            </div>
            <!-- Leave a Review -->
            <?php
            ensureReviewsTable($pdo);
            if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_review'])) {
                $rating = max(1, min(5, (int)($_POST['rating'] ?? 5)));
                $comment = trim($_POST['comment'] ?? '');
                if ($comment !== '') {
                    $stmt = $pdo->prepare('INSERT INTO reviews (user_id, rating, comment, approved) VALUES (?, ?, ?, 1)');
                    $stmt->execute([$user_id, $rating, $comment]);
                    $success = $success ? $success . ' Review submitted!' : 'Review submitted!';
                } else {
                    $error = $error ? $error . ' Please add a comment to your review.' : 'Please add a comment to your review.';
                }
            }
            ?>
            <div class="profile-card p-4 mb-4">
                <h5 class="mb-3">🗣️ Leave a Review</h5>
                <form method="post">
                    <div class="mb-3">
                        <label class="form-label">Rating</label>
                        <select class="form-select" name="rating">
                            <option value="5">★★★★★</option>
                            <option value="4">★★★★☆</option>
                            <option value="3">★★★☆☆</option>
                            <option value="2">★★☆☆☆</option>
                            <option value="1">★☆☆☆☆</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Comment</label>
                        <textarea class="form-control" name="comment" rows="3" placeholder="Share your experience..."></textarea>
                    </div>
                    <button type="submit" name="submit_review" class="btn btn-primary">Submit Review</button>
                </form>
            </div>


        </div>

            </div>
</div>

<!-- Upgrade Modal -->
<div class="modal fade" id="upgradeModal" tabindex="-1" aria-labelledby="upgradeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="upgradeModalLabel">
                    <i class="bi bi-rocket-takeoff"></i> Upgrade Your Account
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Plans Comparison -->
                <div class="row mb-4">
                    <?php foreach ($plans as $plan): ?>
                    <div class="col-md-6 mb-3">
                        <div class="card h-100 <?= $plan['name'] === 'Pro' ? 'border-primary' : '' ?>">
                            <?php if ($plan['name'] === 'Pro'): ?>
                                <div class="card-header bg-info text-white text-center">
                                    <i class="bi bi-star-fill"></i> Most Popular
                                </div>
                            <?php endif; ?>
                            <div class="card-body text-center">
                                <h5 class="card-title"><?= $plan['name'] ?> Plan</h5>
                                <div class="display-6 text-primary mb-3">$<?= $plan['amount'] ?>/month</div>
                                <p class="card-text"><?= $plan['benefits'] ?></p>
                                <div class="d-grid">
                                    <button type="button" class="btn <?= $plan['name'] === 'Pro' ? 'btn-primary' : 'btn-outline-primary' ?> select-plan-btn"
                                            data-plan="<?= $plan['name'] ?>" data-amount="<?= $plan['amount'] ?>">
                                        Select <?= $plan['name'] ?>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <!-- Payment Form -->
                <div id="paymentForm" style="display: none;">
                    <hr>
                    <h6 class="mb-3"><i class="bi bi-credit-card"></i> Payment Details</h6>

                    <div class="alert alert-info">
                        <strong><i class="bi bi-info-circle"></i> Payment Instructions:</strong><br>
                        Send payment to: <strong class="text-primary fs-5">0599672113</strong><br>
                        <small>After payment, enter your transaction reference below.</small>
                    </div>

                    <form method="post" id="upgradeForm">
                        <input type="hidden" name="plan" id="selectedPlan">

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="reference" class="form-label">Transaction Reference *</label>
                                <input type="text" class="form-control" id="reference" name="reference"
                                       placeholder="Enter transaction ID" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="method" class="form-label">Payment Method *</label>
                                <select class="form-select" id="method" name="method" required>
                                    <option value="">Choose payment method...</option>
                                    <option value="MTN">MTN Mobile Money</option>
                                    <option value="Vodafone">Vodafone Cash</option>
                                    <option value="AirtelTigo">AirtelTigo Money</option>
                                    <option value="Bank">Bank Transfer</option>
                                </select>
                            </div>
                        </div>

                        <div class="selected-plan-info p-3 bg-light rounded mb-3" style="display: none;">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>Selected Plan: <span id="planName"></span></strong><br>
                                    <small class="text-muted">Amount: $<span id="planAmount">/month</span></small>
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="resetPlanSelection()">
                                    Change Plan
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="upgradeForm" name="submit_payment" class="btn btn-primary" id="submitPayment" style="display: none;">
                    <i class="bi bi-check-circle"></i> Submit Payment
                </button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
// Plan selection functionality
document.querySelectorAll('.select-plan-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        const plan = this.dataset.plan;
        const amount = this.dataset.amount;

        // Update form
        document.getElementById('selectedPlan').value = plan;
        document.getElementById('planName').textContent = plan;
        document.getElementById('planAmount').textContent = amount;

        // Show payment form and selected plan info
        document.getElementById('paymentForm').style.display = 'block';
        document.querySelector('.selected-plan-info').style.display = 'block';
        document.getElementById('submitPayment').style.display = 'inline-block';

        // Update button states
        document.querySelectorAll('.select-plan-btn').forEach(b => {
            b.classList.remove('btn-success');
            b.classList.add(b.dataset.plan === 'Pro' ? 'btn-primary' : 'btn-outline-primary');
            b.innerHTML = `Select ${b.dataset.plan}`;
        });

        this.classList.remove('btn-primary', 'btn-outline-primary');
        this.classList.add('btn-success');
        this.innerHTML = `<i class="bi bi-check"></i> ${plan} Selected`;
    });
});

function resetPlanSelection() {
    document.getElementById('paymentForm').style.display = 'none';
    document.querySelector('.selected-plan-info').style.display = 'none';
    document.getElementById('submitPayment').style.display = 'none';

    // Reset buttons
    document.querySelectorAll('.select-plan-btn').forEach(btn => {
        btn.classList.remove('btn-success');
        btn.classList.add(btn.dataset.plan === 'Pro' ? 'btn-primary' : 'btn-outline-primary');
        btn.innerHTML = `Select ${btn.dataset.plan}`;
    });

    // Clear form
    document.getElementById('upgradeForm').reset();
}

// Reset modal when closed
document.getElementById('upgradeModal').addEventListener('hidden.bs.modal', function() {
    resetPlanSelection();
});
</script>
</body>
</html>

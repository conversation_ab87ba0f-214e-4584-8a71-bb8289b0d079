# Health Oracle

Health Oracle is a web-based application designed to assist users in diagnosing health conditions, managing medical history, and accessing healthcare resources. It features user authentication, diagnosis tools, transaction history, and an admin dashboard for managing users and settings.

## Features
- User registration and login
- Health diagnosis tool
- Medical history tracking
- Transaction management
- Admin dashboard for user and settings management
- Responsive design with custom CSS and JavaScript

## Admin Credentials
- <EMAIL>/dss_project

## Project Structure
```
├── dashboard.php
├── diagnose.php
├── guest-diagnose.php
├── history.php
├── index.php
├── invest.php
├── login.php
├── logout.php
├── manual_upgrade.php
├── profile.php
├── register.php
├── result.php
├── transactions.php
├── admin/
│   ├── dashboard.php
│   ├── index.php
│   ├── settings.php
│   ├── transactions.php
│   └── users.php
├── api/
│   └── diagnose.php
├── assets/
│   ├── css/
│   │   └── style.css
│   ├── images/
│   └── js/
│       └── main.js
├── includes/
│   ├── auth.php
│   ├── config.php
│   ├── db.php
│   ├── functions.php
│   └── init_db.php
```

## Getting Started
1. Clone the repository or copy the project files to your web server directory.
2. Ensure you have PHP and a MySQL-compatible database installed.
3. Configure database settings in `includes/config.php`.
4. Import the database schema using `includes/init_db.php`.
5. Access the application via your web browser at `http://localhost/health-oracle`.

## Requirements
- PHP 7.0 or higher
- MySQL or compatible database
- Web server (e.g., XAMPP, Apache)

## Usage
- Register a new account or log in.
- Use the diagnosis tool to check symptoms.
- View and manage your medical history and transactions.
- Admins can manage users and settings from the admin dashboard.

## License
This project is provided for educational purposes. Please review and update the license as needed for your use case.

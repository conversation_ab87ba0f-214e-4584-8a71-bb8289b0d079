<?php
require_once 'includes/auth.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

if (!is_logged_in()) {
    header('Location: /health-oracle/login.php');
    exit();
}

$user_id = $_SESSION['user_id'];
$error = '';
$ai_response = '';
$input_symptoms = '';

// Check usage limits for non-admin users
if (!is_admin()) {
    $stmt = $pdo->prepare("SELECT usage_count, subscription_plan, subscription_expires FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch();

    // Check if subscription is active
    $has_active_subscription = false;
    if ($user['subscription_plan'] && $user['subscription_plan'] !== 'free') {
        if ($user['subscription_expires'] && $user['subscription_expires'] >= date('Y-m-d')) {
            $has_active_subscription = true;
        }
    }

    // Set usage limits based on subscription
    $daily_limit = 5; // Default for free users
    if ($has_active_subscription) {
        if ($user['subscription_plan'] === 'basic') {
            $daily_limit = 50; // Basic plan: 50 diagnoses per day
        } elseif ($user['subscription_plan'] === 'pro') {
            $daily_limit = 999999; // Pro plan: unlimited (very high limit)
        }
    }

    if ($user['usage_count'] >= $daily_limit) {
        if ($has_active_subscription) {
            $error = 'You have reached your daily limit. Your ' . ucfirst($user['subscription_plan']) . ' plan allows ' . $daily_limit . ' diagnoses per day.';
        } else {
            $error = 'You have reached your daily limit of 5 diagnoses. Please upgrade your account for unlimited access.';
        }
    }
}

// Handle diagnosis submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !$error) {
    require_once 'includes/functions.php';
    require_once 'includes/config.php';

    // Debug: Check what we're receiving
    $raw_symptoms = $_POST['symptoms'] ?? '';
    $input_symptoms = sanitize_input($raw_symptoms);

    // More robust validation
    if (!empty($input_symptoms) && strlen(trim($input_symptoms)) > 0) {
        // If the input is too short or vague, ask follow-up questions instead of analyzing
        if (function_exists('isAmbiguousInput') && isAmbiguousInput($input_symptoms)) {
            require_once 'includes/functions.php';
            $ai_response = renderFollowUpQuestionsCard($input_symptoms);
        } else {

        // Create enhanced prompt for better AI responses
        $prompt = "You are Dr. HealthOracle, an expert medical AI with advanced diagnostic capabilities, trained on extensive medical literature and clinical guidelines. You provide thorough, evidence-based health assessments.


PATIENT PRESENTATION: \"$input_symptoms\"

ANALYSIS PROTOCOL:
1. SYMPTOM EXTRACTION: Identify all symptoms (explicit, implied, or described)
2. PATTERN RECOGNITION: Analyze symptom clusters and relationships
3. DIFFERENTIAL DIAGNOSIS: Consider multiple conditions with evidence-based reasoning
4. RISK STRATIFICATION: Assess urgency based on clinical severity
5. CLINICAL RECOMMENDATIONS: Provide specific, actionable guidance

RESPONSE FORMAT (use exact HTML structure):

<div class=\"analysis-results\">
<div class=\"alert alert-info mb-4\">
<h4><i class=\"fas fa-user-md\"></i> Medical Assessment</h4>
<p class=\"mb-0\">Professional health analysis based on reported symptoms</p>
</div>

<div class=\"result-section mb-4\">
<div class=\"condition-card p-4 border-start border-primary border-4\" style=\"background: #f8f9fa;\">
<h5><i class=\"fas fa-stethoscope text-primary\"></i> Primary Assessment</h5>
<p class=\"mb-2\"><strong>Most Likely Condition:</strong> [Specific medical condition with confidence level]</p>
<p class=\"mb-2\"><strong>Supporting Evidence:</strong> [Key symptoms that support this diagnosis]</p>
<p class=\"mb-0\"><strong>Alternative Considerations:</strong> [Other possible conditions to monitor]</p>
</div>
</div>

<div class=\"result-section mb-4\">
<div class=\"urgency-card p-4\" style=\"background: #fff3cd; border-left: 4px solid #ffc107;\">
<h5><i class=\"fas fa-exclamation-triangle text-warning\"></i> Urgency Assessment</h5>
<div class=\"d-flex align-items-center mb-2\">
<span class=\"badge bg-warning text-dark fs-6 me-2\">[MILD/MODERATE/URGENT]</span>
<strong>[Timeframe for medical attention]</strong>
</div>
<p class=\"mb-0\"><strong>Rationale:</strong> [Clinical reasoning for urgency level with specific red flags if present]</p>
</div>
</div>

<div class=\"result-section mb-4\">
<div class=\"recommendations-card p-4\" style=\"background: #d1ecf1; border-left: 4px solid #17a2b8;\">
<h5><i class=\"fas fa-clipboard-list text-info\"></i> Clinical Recommendations</h5>
<div class=\"row\">
<div class=\"col-md-6\">
<p class=\"mb-2\"><strong>Immediate Actions:</strong></p>
<ul class=\"mb-3\">
[Specific immediate steps]
</ul>
<p class=\"mb-2\"><strong>Home Management:</strong></p>
<ul class=\"mb-0\">
[Detailed self-care measures]
</ul>
</div>
<div class=\"col-md-6\">
<p class=\"mb-2\"><strong>Medical Consultation:</strong></p>
<ul class=\"mb-3\">
[When and why to see a doctor]
</ul>
<p class=\"mb-2\"><strong>Warning Signs:</strong></p>
<ul class=\"mb-0\">
[Red flags requiring immediate attention]
</ul>
</div>
</div>
</div>
</div>
    <div class=\"result-section mb-4\">\n    <div class=\"p-4\" style=\"background: #ffffff; border-left: 4px solid #6c757d;\">\n    <h5><i class=\"fas fa-brain text-secondary\"></i> Explain Reasoning</h5>\n    <details>\n    <summary>Click to expand clinical reasoning</summary>\n    <div class=\"mt-2 small text-muted\">[Step-by-step clinical reasoning that links symptoms to the assessment and differential. Note uncertainties and what additional information would change the assessment.]</div>\n    </details>\n    </div>\n    </div>\n

<div class=\"alert alert-warning mt-4\">
<h6><i class=\"fas fa-shield-alt\"></i> Medical Disclaimer</h6>
<p class=\"mb-0 small\">This AI assessment is for informational purposes only. Always consult qualified healthcare professionals for proper diagnosis and treatment. Seek immediate medical attention for severe or worsening symptoms.</p>
</div>
</div>

CRITICAL REQUIREMENTS:
- Provide SPECIFIC medical conditions, not vague descriptions
- Use EVIDENCE-BASED reasoning with clinical correlation
- Include DIFFERENTIAL diagnoses when appropriate
- Be PRECISE with urgency levels and timeframes
- Give ACTIONABLE recommendations with clear steps
- Consider PATIENT SAFETY as the highest priority
- Use proper medical terminology with clear explanations
- Format response EXACTLY as specified above
- Never provide generic or superficial analysis";

        try {
            $ai_response = callOpenAI($prompt);

            // Extract text summary for storage
            // Rebuild prompt cleanly to avoid undefined $prompt issues
            $prompt = <<<PROMPT
You are Dr. HealthOracle, an expert medical AI with advanced diagnostic capabilities, trained on extensive medical literature and clinical guidelines. You provide thorough, evidence-based health assessments.

PATIENT PRESENTATION: "{$input_symptoms}"

ANALYSIS PROTOCOL:
1. SYMPTOM EXTRACTION: Identify all symptoms (explicit, implied, or described)
2. PATTERN RECOGNITION: Analyze symptom clusters and relationships
3. DIFFERENTIAL DIAGNOSIS: Consider multiple conditions with evidence-based reasoning
4. RISK STRATIFICATION: Assess urgency based on clinical severity
5. CLINICAL RECOMMENDATIONS: Provide specific, actionable guidance

RESPONSE FORMAT (use exact HTML structure):

<div class="analysis-results">
<div class="alert alert-info mb-4">
<h4><i class="fas fa-user-md"></i> Medical Assessment</h4>
<p class="mb-0">Professional health analysis based on reported symptoms</p>
</div>

<div class="result-section mb-4">
<div class="condition-card p-4 border-start border-primary border-4" style="background: #f8f9fa;">
<h5><i class="fas fa-stethoscope text-primary"></i> Primary Assessment</h5>
<p class="mb-2"><strong>Most Likely Condition:</strong> [Specific medical condition with confidence level]</p>
<p class="mb-2"><strong>Supporting Evidence:</strong> [Key symptoms that support this diagnosis]</p>
<p class="mb-0"><strong>Alternative Considerations:</strong> [Other possible conditions to monitor]</p>
</div>
</div>

<div class="result-section mb-4">
<div class="urgency-card p-4" style="background: #fff3cd; border-left: 4px solid #ffc107;">
<h5><i class="fas fa-exclamation-triangle text-warning"></i> Urgency Assessment</h5>
<div class="d-flex align-items-center mb-2">
<span class="badge bg-warning text-dark fs-6 me-2">[MILD/MODERATE/URGENT]</span>
<strong>[Timeframe for medical attention]</strong>
</div>
<p class="mb-0"><strong>Rationale:</strong> [Clinical reasoning for urgency level with specific red flags if present]</p>
</div>
</div>

<div class="result-section mb-4">
<div class="recommendations-card p-4" style="background: #d1ecf1; border-left: 4px solid #17a2b8;">
<h5><i class="fas fa-clipboard-list text-info"></i> Clinical Recommendations</h5>
<div class="row">
<div class="col-md-6">
<p class="mb-2"><strong>Immediate Actions:</strong></p>
<ul class="mb-3">
[Specific immediate steps]
</ul>
<p class="mb-2"><strong>Home Management:</strong></p>
<ul class="mb-0">
[Detailed self-care measures]
</ul>
</div>
<div class="col-md-6">
<p class="mb-2"><strong>Medical Consultation:</strong></p>
<ul class="mb-3">
[When and why to see a doctor]
</ul>
<p class="mb-2"><strong>Warning Signs:</strong></p>
<ul class="mb-0">
[Red flags requiring immediate attention]
</ul>
</div>
</div>
</div>
</div>
    <div class="result-section mb-4">
    <div class="p-4" style="background: #ffffff; border-left: 4px solid #6c757d;">
    <h5><i class="fas fa-brain text-secondary"></i> Explain Reasoning</h5>
    <details>
    <summary>Click to expand clinical reasoning</summary>
    <div class="mt-2 small text-muted">[Step-by-step clinical reasoning that links symptoms to the assessment and differential. Note uncertainties and what additional information would change the assessment.]</div>
    </details>
    </div>
    </div>

<div class="alert alert-warning mt-4">
<h6><i class="fas fa-shield-alt"></i> Medical Disclaimer</h6>
<p class="mb-0 small">This AI assessment is for informational purposes only. Always consult qualified healthcare professionals for proper diagnosis and treatment. Seek immediate medical attention for severe or worsening symptoms.</p>
</div>
</div>

CRITICAL REQUIREMENTS:
- Provide SPECIFIC medical conditions, not vague descriptions
- Use EVIDENCE-BASED reasoning with clinical correlation
- Include DIFFERENTIAL diagnoses when appropriate
- Be PRECISE with urgency levels and timeframes
- Give ACTIONABLE recommendations with clear steps
- Consider PATIENT SAFETY as the highest priority
- Use proper medical terminology with clear explanations
- Format response EXACTLY as specified above
- Avoid generic phrases like 'rest, stay hydrated, manage stress' unless medically necessary; if used, be specific and explain rationale
PROMPT;

            if (defined('AI_PROVIDER') && AI_PROVIDER === 'hybrid') {
                $prompt = enrichPromptWithRapidAPI($input_symptoms, $prompt);
            }

            $text_summary = extractTextSummary($ai_response);

            // Store in database
            $stmt = $pdo->prepare('INSERT INTO diagnoses (user_id, input_text, ai_response, text_summary) VALUES (?, ?, ?, ?)');
            $stmt->execute([$user_id, $input_symptoms, $ai_response, $text_summary]);

            // Update usage count
            $today = date('Y-m-d');
            $pdo->prepare('UPDATE users SET usage_count = usage_count + 1, last_usage_date = ? WHERE id = ?')->execute([$today, $user_id]);

        } catch (Exception $e) {
            $error = 'Sorry, we are experiencing technical difficulties. Please try again later.';
            error_log("Diagnosis error: " . $e->getMessage());
        }
        }
    } else {
        $error = 'Please enter your symptoms.';
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Diagnosis - Health Oracle</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
    <style>
        body {
            font-family: 'Poppins', 'Inter', sans-serif;
            background: #F8F9FA;
            min-height: 100vh;
        }
        .diagnose-card {
            border-radius: 1rem;
            box-shadow: 0 2px 16px rgba(70,130,180,0.08);
            background: #fff;
            max-width: 1000px;
            margin: 0 auto;
        }
        .btn-primary { background: #007BFF; border: none; }
        .btn-primary:hover { background: #1E90FF; }
        .symptom-input-header {
            background: #ADD8E6;
            color: #003366;
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0;
            padding: 1.5rem 2rem 0.5rem;
            border-top-left-radius: 1rem;
            border-top-right-radius: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }
        .symptom-input-subtitle {
            background: #E6F2FF;
            color: #003366;
            margin-bottom: 0;
            padding: 0 2rem 1.5rem;
            text-align: center;
            font-size: 0.95rem;
        }
        .input-section {
            background: white;
            border-radius: 0;
            border-bottom-left-radius: 1rem;
            border-bottom-right-radius: 1rem;
            padding: 2rem;
            margin: 0;
        }
        .toggle-btns {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 2rem;
            justify-content: center;
        }
        .toggle-btns .btn {
            border-radius: 2rem;
            font-weight: 500;
            padding: 0.6rem 1.5rem;
            border: 1px solid #E2E8F0;
            background: white;
            color: #64748B;
            transition: all 0.3s ease;
        }
        .toggle-btns .btn.active {
            background: #ADD8E6;
            color: white;
            border-color: #ADD8E6;
        }
        .form-control {
            border-radius: 0.75rem;
            border: 1px solid #E2E8F0;
            padding: 0.9rem 1rem;
            font-size: 1rem;
        }
        .form-control:focus {
            border-color: #ADD8E6;
            box-shadow: 0 0 0 0.2rem rgba(173,216,230,0.5);
        }
        .example-text {
            font-size: 0.85rem;
            color: #64748B;
            margin-bottom: 0.75rem;
        }
        .btn-analyze {
            background: #007BFF;
            border: none;
            border-radius: 2rem;
            padding: 0.75rem 2rem;
            font-weight: 600;
            color: white;
            transition: all 0.3s ease;
        }
        .btn-analyze:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
            color: white;
        }
        .navbar-toggler { border: 1px solid #333; }
        .navbar-toggler-icon { background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2833, 37, 41, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e"); }
    </style>
</head>
<body>
<?= getNavbarHtml() ?>

<div class="container py-5" style="max-width: 1200px;">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="diagnose-card">
                <div class="symptom-input-header">
                    🩺 AI Health Analysis
                </div>
                <div class="symptom-input-subtitle">
                    Describe your symptoms and get instant AI-powered health insights
                </div>
            <?= renderConfigBanner() ?>


                <div class="input-section">
                    <?php if ($error): ?>
                        <div class="alert alert-danger mb-3"> <?= htmlspecialchars($error) ?> </div>
                        <div class="text-center">
                            <a href="/health-oracle/invest.php" class="btn btn-primary">Upgrade Account</a>
                            <a href="/health-oracle/dashboard.php" class="btn btn-secondary">Back to Dashboard</a>
                        </div>
                    <?php else: ?>

                    <div class="toggle-btns">
                        <button type="button" class="btn active" id="btn-keywords">Keywords</button>
                        <button type="button" class="btn" id="btn-description">Description</button>
                    </div>

                    <form method="post" id="symptom-form">
                        <input type="hidden" name="user_id" value="<?= $user_id ?>">
                        <!-- Unified field populated by JS on submit -->
                        <input type="hidden" name="symptoms" id="symptoms-hidden" value="<?= htmlspecialchars($input_symptoms) ?>">

                        <div id="keywords-input">
                            <div class="form-group">
                                <label class="form-label fw-semibold">Enter symptom keywords (separated by commas)</label>
                                <p class="example-text">Example: fever, cough, sore throat, headache, nausea, fatigue</p>
                                <input type="text" class="form-control" name="symptoms_keywords" placeholder="fever, cough, sore throat, headache..."
                                       value="<?= htmlspecialchars($input_symptoms) ?>" required>
                                <small class="form-text text-muted">Tip: Use simple, clear keywords for best results</small>
                            </div>
                        </div>

                        <div id="description-input" style="display: none;">
                            <div class="form-group">
                                <label class="form-label fw-semibold">Describe your symptoms in detail</label>
                                <p class="example-text">Example: I've been feeling tired for the past 3 days, with a mild headache and occasional cough. I also have a sore throat and slight fever.</p>
                                <textarea class="form-control" name="symptoms_description" rows="8" placeholder="Describe how you're feeling, when symptoms started, their severity, and any other relevant details..."><?= htmlspecialchars($input_symptoms) ?></textarea>
                                <small class="form-text text-muted">Tip: Include timing, severity, and any triggers for better analysis</small>
                            </div>
                        </div>

                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-analyze">
                                🔍 Analyze Symptoms
                            </button>
                        </div>
                    </form>

                    <?php endif; ?>
                </div>
            </div>

            <?php if ($ai_response): ?>
            <!-- Results Section -->
            <div class="diagnose-card mt-4">
                <div class="symptom-input-header">
                    🎯 Analysis Results
                </div>
                <div class="input-section">
                    <?= $ai_response ?>

                    <div class="text-center mt-4">
                        <a href="/health-oracle/diagnose.php" class="btn btn-secondary me-2">New Diagnosis</a>
                        <a href="/health-oracle/dashboard.php" class="btn btn-primary">Back to Dashboard</a>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <div class="text-center mt-4">
                <p class="text-daark-50 small">
                    ⚠️ This tool provides general health information only and should not replace professional medical advice.
                </p>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
// Toggle input modes
document.addEventListener('DOMContentLoaded', function() {
    const btnKeywords = document.getElementById('btn-keywords');
    const btnDescription = document.getElementById('btn-description');
    const keywordsInput = document.getElementById('keywords-input');
    const descriptionInput = document.getElementById('description-input');
    const hiddenSymptoms = document.getElementById('symptoms-hidden');
    const form = document.getElementById('symptom-form');

    if (btnKeywords && btnDescription && keywordsInput && descriptionInput && hiddenSymptoms && form) {
        btnKeywords.addEventListener('click', function() {
            btnKeywords.classList.add('active');
            btnDescription.classList.remove('active');
            keywordsInput.style.display = 'block';
            descriptionInput.style.display = 'none';

            const keywordInput = keywordsInput.querySelector('input[name="symptoms_keywords"]');
            const descTextarea = descriptionInput.querySelector('textarea[name="symptoms_description"]');

            if (keywordInput) keywordInput.required = true;
            if (descTextarea) descTextarea.required = false;
        });

        btnDescription.addEventListener('click', function() {
            btnDescription.classList.add('active');
            btnKeywords.classList.remove('active');
            keywordsInput.style.display = 'none';
            descriptionInput.style.display = 'block';

            const keywordInput = keywordsInput.querySelector('input[name="symptoms_keywords"]');
            const descTextarea = descriptionInput.querySelector('textarea[name="symptoms_description"]');

            if (descTextarea) descTextarea.required = true;
            if (keywordInput) keywordInput.required = false;
        });

        // Before submit, populate the hidden unified field with whichever is active
        form.addEventListener('submit', function(e) {
            const isKeywordsActive = btnKeywords.classList.contains('active');
            const keywordInput = keywordsInput.querySelector('input[name="symptoms_keywords"]');
            const descTextarea = descriptionInput.querySelector('textarea[name="symptoms_description"]');

            const value = isKeywordsActive ? (keywordInput ? keywordInput.value : '')
                                           : (descTextarea ? descTextarea.value : '');
            hiddenSymptoms.value = value;
        });

        // Optional: handle follow-up button to switch to Description tab
        const followBtn = document.getElementById('answer-questions-btn');
        if (followBtn && btnDescription) {
            followBtn.addEventListener('click', function() {
                btnDescription.click();
                window.scrollTo({ top: 0, behavior: 'smooth' });
            });
        }
    }
});
</script>
</body>
</html>

<?php
// Manual user upgrade script for testing
require_once __DIR__ . '/includes/db.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $user_id = (int)$_POST['user_id'];
    $plan = $_POST['plan'];
    $days = (int)$_POST['days'];
    
    if ($user_id && $plan && $days) {
        $expiry_date = date('Y-m-d', strtotime("+{$days} days"));
        
        $stmt = $pdo->prepare('UPDATE users SET subscription_plan = ?, subscription_expires = ?, usage_count = 0 WHERE id = ?');
        $stmt->execute([$plan, $expiry_date, $user_id]);
        
        echo "<div class='alert alert-success'>User {$user_id} upgraded to {$plan} plan until {$expiry_date}</div>";
    }
}

// Get all users
$users = $pdo->query('SELECT id, name, email, subscription_plan, subscription_expires FROM users ORDER BY id')->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual User Upgrade - Health Oracle</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container py-5">
    <div class="card">
        <div class="card-header">
            <h3>Manual User Upgrade Tool</h3>
            <p class="text-muted">For testing purposes only</p>
        </div>
        <div class="card-body">
            <form method="POST">
                <div class="row">
                    <div class="col-md-3">
                        <label for="user_id" class="form-label">User ID</label>
                        <input type="number" class="form-control" id="user_id" name="user_id" required>
                    </div>
                    <div class="col-md-3">
                        <label for="plan" class="form-label">Plan</label>
                        <select class="form-control" id="plan" name="plan" required>
                            <option value="">Select Plan</option>
                            <option value="basic">Basic</option>
                            <option value="pro">Pro</option>
                            <option value="free">Free</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="days" class="form-label">Days</label>
                        <input type="number" class="form-control" id="days" name="days" value="30" required>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary d-block">Upgrade User</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <div class="card mt-4">
        <div class="card-header">
            <h4>All Users</h4>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Plan</th>
                            <th>Expires</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                        <tr>
                            <td><?= $user['id'] ?></td>
                            <td><?= htmlspecialchars($user['name']) ?></td>
                            <td><?= htmlspecialchars($user['email']) ?></td>
                            <td><?= $user['subscription_plan'] ?: 'free' ?></td>
                            <td><?= $user['subscription_expires'] ?: 'N/A' ?></td>
                            <td>
                                <?php 
                                if ($user['subscription_plan'] && $user['subscription_plan'] !== 'free') {
                                    if ($user['subscription_expires'] && $user['subscription_expires'] >= date('Y-m-d')) {
                                        echo '<span class="badge bg-success">Active</span>';
                                    } else {
                                        echo '<span class="badge bg-warning">Expired</span>';
                                    }
                                } else {
                                    echo '<span class="badge bg-secondary">Free</span>';
                                }
                                ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
</body>
</html>

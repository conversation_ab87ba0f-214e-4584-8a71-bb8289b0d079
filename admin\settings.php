<?php
require_once __DIR__ . '/../includes/auth.php';
require_admin();
require_once __DIR__ . '/../includes/db.php';
require_once __DIR__ . '/../includes/functions.php';

// Fetch current settings
$settings = $pdo->query('SELECT * FROM settings WHERE id=1')->fetch();
if (!$settings) {
    $pdo->exec('INSERT INTO settings (id, guest_limit, user_limit) VALUES (1, 3, 10)');
    $settings = ['guest_limit' => 3, 'user_limit' => 10];
}

// Update settings
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $guest_limit = (int)($_POST['guest_limit'] ?? 3);
    $user_limit = (int)($_POST['user_limit'] ?? 10);
    $pdo->prepare('UPDATE settings SET guest_limit=?, user_limit=? WHERE id=1')->execute([$guest_limit, $user_limit]);
    $settings['guest_limit'] = $guest_limit;
    $settings['user_limit'] = $user_limit;
}
?><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Settings | Health Oracle</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&family=Inter:wght@400;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
    <style>
        body { font-family: 'Poppins', 'Inter', sans-serif; background: #F8F9FA; }
        .admin-card { border-radius: 1rem; box-shadow: 0 2px 16px rgba(70,130,180,0.08); background: #fff; }
    </style>
</head>
<body>
<?= getNavbarHtml() ?>
<div class="container py-5">
    <div class="admin-card p-4">
        <h2 class="mb-4" style="color:#007BFF;">Settings</h2>
        <form method="post">
            <div class="mb-3">
                <label for="guest_limit" class="form-label">Guest Daily Usage Limit</label>
                <input type="number" class="form-control" id="guest_limit" name="guest_limit" value="<?= (int)$settings['guest_limit'] ?>" min="1" required>
            </div>
            <div class="mb-3">
                <label for="user_limit" class="form-label">User Daily Usage Limit</label>
                <input type="number" class="form-control" id="user_limit" name="user_limit" value="<?= (int)$settings['user_limit'] ?>" min="1" required>
            </div>
            <button type="submit" class="btn btn-primary">Save Settings</button>
        </form>
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
require_once __DIR__ . '/../includes/auth.php';
require_admin();
require_once __DIR__ . '/../includes/db.php';
require_once __DIR__ . '/../includes/functions.php';

// Fetch current settings
$settings = $pdo->query('SELECT * FROM settings WHERE id=1')->fetch();
if (!$settings) {
    $pdo->exec('INSERT INTO settings (id, guest_limit, user_limit) VALUES (1, 3, 10)');
    $settings = ['guest_limit' => 3, 'user_limit' => 10];
}

// Update settings
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $guest_limit = (int)($_POST['guest_limit'] ?? 3);
    $user_limit = (int)($_POST['user_limit'] ?? 10);
    $pdo->prepare('UPDATE settings SET guest_limit=?, user_limit=? WHERE id=1')->execute([$guest_limit, $user_limit]);
    $settings['guest_limit'] = $guest_limit;
    $settings['user_limit'] = $user_limit;
}
?><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Settings | Health Oracle</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="/assets/css/style.css">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        .admin-card {
            border-radius: 1.5rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            background: #fff;
            border: none;
            transition: transform 0.3s ease;
        }
        .admin-card:hover {
            transform: translateY(-2px);
        }
        .settings-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 1.5rem 1.5rem 0 0;
            position: relative;
            overflow: hidden;
        }
        .settings-header::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 150px;
            height: 150px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            transform: translate(50px, -50px);
        }
        .form-control {
            border-radius: 1rem;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 1rem;
            padding: 0.75rem 2rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        .setting-card {
            border: 2px solid #e9ecef;
            border-radius: 1rem;
            transition: all 0.3s ease;
        }
        .setting-card:hover {
            border-color: #667eea;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
        }
    </style>
</head>
<body>
<?= getNavbarHtml() ?>
<div class="container py-5">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-0">
                <div class="settings-header p-5 position-relative">
                    <h1 class="h2 mb-3">
                        <i class="bi bi-gear-fill me-3"></i>System Settings
                    </h1>
                    <p class="mb-0 opacity-90">Configure usage limits and system parameters</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Success Message -->
    <?php if ($_SERVER['REQUEST_METHOD'] === 'POST'): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle"></i> Settings updated successfully!
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Settings Form -->
    <div class="row g-4">
        <div class="col-lg-8">
            <div class="admin-card p-4">
                <h5 class="mb-4">
                    <i class="bi bi-sliders me-2"></i>Usage Limits Configuration
                </h5>

                <form method="post">
                    <div class="row g-4">
                        <div class="col-md-6">
                            <div class="setting-card p-4">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="bg-info text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 48px; height: 48px;">
                                        <i class="bi bi-person-walking"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0">Guest Users</h6>
                                        <small class="text-muted">Unregistered visitors</small>
                                    </div>
                                </div>
                                <label for="guest_limit" class="form-label fw-semibold">Daily Diagnosis Limit</label>
                                <input type="number" class="form-control" id="guest_limit" name="guest_limit"
                                       value="<?= (int)$settings['guest_limit'] ?>" min="1" max="50" required>
                                <div class="form-text">
                                    <i class="bi bi-info-circle"></i> Number of diagnoses guests can perform per day
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="setting-card p-4">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 48px; height: 48px;">
                                        <i class="bi bi-person-check"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0">Free Users</h6>
                                        <small class="text-muted">Registered free accounts</small>
                                    </div>
                                </div>
                                <label for="user_limit" class="form-label fw-semibold">Daily Diagnosis Limit</label>
                                <input type="number" class="form-control" id="user_limit" name="user_limit"
                                       value="<?= (int)$settings['user_limit'] ?>" min="1" max="100" required>
                                <div class="form-text">
                                    <i class="bi bi-info-circle"></i> Number of diagnoses free users can perform per day
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center mt-4 pt-4 border-top">
                        <div class="text-muted">
                            <i class="bi bi-shield-check"></i> Changes take effect immediately
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-lg me-2"></i>Save Settings
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Current Status -->
            <div class="admin-card p-4 mb-4">
                <h6 class="mb-3">
                    <i class="bi bi-graph-up me-2"></i>Current Status
                </h6>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="text-muted">Guest Limit:</span>
                    <span class="badge bg-info"><?= $settings['guest_limit'] ?> per day</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="text-muted">Free User Limit:</span>
                    <span class="badge bg-success"><?= $settings['user_limit'] ?> per day</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span class="text-muted">Pro Users:</span>
                    <span class="badge bg-primary">Unlimited</span>
                </div>
            </div>

            <!-- Information -->
            <div class="admin-card p-4">
                <h6 class="mb-3">
                    <i class="bi bi-lightbulb me-2"></i>Information
                </h6>
                <div class="small text-muted">
                    <p class="mb-2">
                        <strong>Guest Users:</strong> Visitors who use the diagnosis tool without registering. Limited by IP address.
                    </p>
                    <p class="mb-2">
                        <strong>Free Users:</strong> Registered users with free accounts. Higher limits than guests.
                    </p>
                    <p class="mb-0">
                        <strong>Pro Users:</strong> Paid subscribers with unlimited access to all features.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

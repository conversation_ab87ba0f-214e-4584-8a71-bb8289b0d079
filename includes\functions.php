<?php
require_once __DIR__ . '/config.php';
// Utility functions for Health Oracle
function sanitize_input($data) {
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

// Centralized, consistent navbar for all pages
function getNavbarHtml() {
    $is_logged_in = function_exists('is_logged_in') ? is_logged_in() : false;
    $is_admin = function_exists('is_admin') ? is_admin() : false;

    ob_start();
    ?>
    <nav class="navbar navbar-expand-lg sticky-top" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); backdrop-filter: blur(10px); box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);">
      <div class="container">
        <a class="navbar-brand text-white fw-bold" href="/health-oracle/">
          <i class="bi bi-heart-pulse me-2"></i>Health Oracle
        </a>
        <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
          <span class="navbar-toggler-icon" style="filter: invert(1);"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav ms-auto mb-2 mb-lg-0">
            <?php if ($is_logged_in): ?>
              <?php if ($is_admin): ?>
                <li class="nav-item"><a class="nav-link text-white-50 hover-text-white" href="/health-oracle/admin/dashboard.php"><i class="bi bi-speedometer2 me-1"></i>Admin Panel</a></li>
                <li class="nav-item"><a class="nav-link text-white-50 hover-text-white" href="/health-oracle/admin/users.php"><i class="bi bi-people me-1"></i>Users</a></li>
                <li class="nav-item"><a class="nav-link text-white-50 hover-text-white" href="/health-oracle/admin/transactions.php"><i class="bi bi-credit-card me-1"></i>Payments</a></li>
                <li class="nav-item"><a class="nav-link text-white-50 hover-text-white" href="/health-oracle/admin/settings.php"><i class="bi bi-gear me-1"></i>Settings</a></li>
              <?php else: ?>
                <li class="nav-item"><a class="nav-link text-white-50 hover-text-white" href="/health-oracle/dashboard.php"><i class="bi bi-house me-1"></i>Dashboard</a></li>
                <li class="nav-item"><a class="nav-link text-white-50 hover-text-white" href="/health-oracle/diagnose.php"><i class="bi bi-stethoscope me-1"></i>New Diagnosis</a></li>
                <li class="nav-item"><a class="nav-link text-white-50 hover-text-white" href="/health-oracle/history.php"><i class="bi bi-clock-history me-1"></i>History</a></li>
                <li class="nav-item"><a class="nav-link text-white-50 hover-text-white" href="/health-oracle/profile.php"><i class="bi bi-person me-1"></i>Profile</a></li>
              <?php endif; ?>
              <li class="nav-item"><a class="nav-link text-white border border-white-50 rounded-pill px-3 ms-2" href="/health-oracle/logout.php"><i class="bi bi-box-arrow-right me-1"></i>Logout</a></li>
            <?php else: ?>
              <li class="nav-item"><a class="nav-link text-white-50 hover-text-white" href="/health-oracle/"><i class="bi bi-house me-1"></i>Home</a></li>
              <li class="nav-item"><a class="nav-link text-white-50 hover-text-white" href="/health-oracle/guest-diagnose.php"><i class="bi bi-lightning me-1"></i>Try as Guest</a></li>
              <li class="nav-item"><a class="nav-link text-white-50 hover-text-white" href="/health-oracle/login.php"><i class="bi bi-box-arrow-in-right me-1"></i>Login</a></li>
              <li class="nav-item"><a class="nav-link text-white border border-white-50 rounded-pill px-3 ms-2" href="/health-oracle/register.php"><i class="bi bi-person-plus me-1"></i>Register</a></li>
            <?php endif; ?>
          </ul>
        </div>
      </div>
    </nav>
    <?php
    return ob_get_clean();
}

// Config banner for admins when keys are missing
function renderConfigBanner() {
    $is_admin = function_exists('is_admin') ? is_admin() : false;
    if (!$is_admin) return '';

    $messages = [];
    if (!defined('OPENAI_API_KEY') || OPENAI_API_KEY === '') {
        $envKey = getenv('OPENAI_API_KEY');
        if (!$envKey) {
            $messages[] = 'OpenAI key missing. Ensure .env contains OPENAI_API_KEY and restart Apache.';
        }
    }
    if (empty($messages)) return '';

    $html = '<div class="alert alert-warning d-flex align-items-center gap-2 mb-4">'
          . '<i class="bi bi-exclamation-triangle-fill me-2"></i>'
          . implode(' ', array_map('htmlspecialchars', $messages))
          . '</div>';

    // Add navbar styles
    $html .= '<style>
        .hover-text-white:hover { color: white !important; transition: color 0.3s ease; }
        .nav-link { transition: all 0.3s ease; }
        .nav-link:hover { transform: translateY(-1px); }
    </style>';

    return $html;
}

// Detect whether the user's input is too vague/short to analyze reliably
function isAmbiguousInput($text) {
    $t = strtolower(trim($text));
    if ($t === '') return true;

    if (mb_strlen($t) < 15) return true; // very short

    $words = preg_split('/[^a-z]+/i', $t, -1, PREG_SPLIT_NO_EMPTY);
    if (count($words) < 3) return true;

    if (strpos($t, ',') !== false) {
        $parts = array_filter(array_map('trim', explode(',', $t)));
        if (count($parts) < 2) return true;
    }

    $generic = ['pain','sick','ill','unwell','not feeling well','bad','issue'];
    foreach ($generic as $g) {
        if ($t === $g) return true;
    }

    return false;
}

// Render a follow-up question card prompting for more detail
function renderFollowUpQuestionsCard($input_text) {
    $questions = [
        'Where is the symptom located (e.g., throat, chest, abdomen)?',
        'How long has this been going on and is it getting better or worse?',
        'How severe is it on a scale of 1–10?',
        'Any associated symptoms (fever, nausea, shortness of breath, rash, dizziness)?',
        'Any recent travel, new medications, or known medical conditions?',
        'Have you tried any treatments and did they help?'
    ];

    $list = '';
    foreach ($questions as $q) {
        $list .= '<li>' . htmlspecialchars($q) . '</li>';
    }

    $html = '
    <div class="analysis-results">
      <div class="alert alert-warning mb-3">
        <strong>We need a bit more detail</strong> to give an accurate assessment.
      </div>
      <div class="result-section mb-3">
        <div class="p-3 bg-light border rounded">
          <h5 class="mb-2"><i class="fas fa-question-circle"></i> Follow-up questions</h5>
          <ol class="mb-0">' . $list . '</ol>
        </div>
      </div>
      <div class="text-center">
        <button type="button" id="answer-questions-btn" class="btn btn-primary">Answer these questions</button>
      </div>
      <p class="small text-muted mt-3 mb-0">Tip: Switch to “Description” and answer in a few short sentences.</p>
    </div>';

    return $html;
}

// Hybrid provider helpers (RapidAPI)
function callRapidAPIMedical($input_text) {
    return null; // RapidAPI disabled
    $payload = [
        "gender" => "n/a",
        "age" => 30,
        "medications" => "",
        "symptoms" => $input_text,
        "conditions" => "",
        "exercise" => "moderate",
        "diet" => "balanced"
    ];
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "https://" . RAPIDAPI_HOST . "/analysisSymptomsAndDiagnosis");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "Content-Type: application/json",
        "x-rapidapi-key: " . RAPIDAPI_KEY,
        "x-rapidapi-host: " . RAPIDAPI_HOST,
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 25);
    $resp = curl_exec($ch);
    $code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $err = curl_error($ch);
    curl_close($ch);
    if ($err || $code < 200 || $code >= 300) {
        if (defined('ENABLE_LOGGING') && ENABLE_LOGGING) {
            error_log("RapidAPI error ($code): $err resp=" . substr((string)$resp, 0, 300));
        }
        return null;
    }
    $decoded = json_decode($resp, true);
    return $decoded ?: null;
}


function callOpenAI($prompt) {
    $api_key = OPENAI_API_KEY;

    if (empty($api_key)) {
        return generateMockResponse($prompt);
    }

    // Prepare structured prompt
    $structured_prompt = prepareStructuredPrompt($prompt);


// Merge RapidAPI structured results into our prompt as a prior signal
function enrichPromptWithRapidAPI($input_text, $base_prompt) {
    // RapidAPI disabled by configuration; do not modify prompt
    return $base_prompt;
}

    $structured_prompt = prepareStructuredPrompt($prompt);

    $data = [
        'model' => defined('OPENAI_MODEL') ? OPENAI_MODEL : 'gpt-3.5-turbo',
        'messages' => [
            [
                'role' => 'system',
                'content' => 'You are Dr. HealthOracle, an expert medical AI assistant with advanced diagnostic capabilities. You provide thorough, evidence-based health assessments with clinical precision. You excel at symptom analysis, differential diagnosis, and clinical recommendations. Always maintain the highest standards of medical accuracy while being clear and actionable. IMPORTANT: This is for informational purposes only and should not replace professional medical advice. Always recommend consulting a healthcare professional for proper diagnosis and treatment.'
            ],
            [
                'role' => 'user',
                'content' => $structured_prompt
            ]
        ],
        'max_tokens' => 1200,
        'temperature' => 0.2,
        'top_p' => 0.9,
        'frequency_penalty' => 0.1,
        'presence_penalty' => 0.1
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://api.openai.com/v1/chat/completions');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $api_key
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);

    // Log API calls if enabled
    if (defined('ENABLE_LOGGING') && ENABLE_LOGGING) {
        error_log("OpenAI API Call - HTTP Code: $http_code, Response Length: " . strlen($response));
    }

    if ($curl_error) {
        error_log("OpenAI API cURL Error: " . $curl_error);
        return generateMockResponse($prompt);
    }

    if ($http_code !== 200) {
        error_log("OpenAI API Error - HTTP Code: $http_code, Response: " . substr($response, 0, 500));
        return generateMockResponse($prompt);
    }

    $decoded = json_decode($response, true);

    if (isset($decoded['choices'][0]['message']['content'])) {
        $ai_response = trim($decoded['choices'][0]['message']['content']);

        // Return the AI response directly since it's already formatted
        return $ai_response;
    } else {
        error_log("OpenAI API - Invalid response structure: " . json_encode($decoded));
        return generateMockResponse($prompt);
    }
}

function prepareStructuredPrompt($symptoms) {
    $base_prompt = "You are a medical AI assistant. Analyze the following symptoms and provide a structured response. ";
    $base_prompt .= "IMPORTANT: This is for informational purposes only and should not replace professional medical advice. ";
    $base_prompt .= "Always recommend consulting a healthcare professional for proper diagnosis and treatment.\n\n";

    // Extract symptoms from the input
    if (preg_match('/reports: "([^"]+)"/', $symptoms, $matches)) {
        $symptom_text = $matches[1];
        $base_prompt .= "The user has described their symptoms as follows: " . $symptom_text . "\n\n";
    } elseif (preg_match('/user reports: "([^"]+)"/', $symptoms, $matches)) {
        $symptom_text = $matches[1];
        $base_prompt .= "The user has described their symptoms as follows: " . $symptom_text . "\n\n";
    } else {
        $base_prompt .= "The user has described their symptoms as follows: " . $symptoms . "\n\n";
    }

    $base_prompt .= "Please provide your analysis in the following format:\n";
    $base_prompt .= "Possible Condition: [Your assessment of the most likely condition]\n";
    $base_prompt .= "Urgency Level: [mild/moderate/urgent]\n";
    $base_prompt .= "Recommendations: [Specific recommendations for next steps]\n\n";

    $base_prompt .= "Guidelines:\n";
    $base_prompt .= "- Be conservative in your assessment\n";
    $base_prompt .= "- Always err on the side of caution\n";
    $base_prompt .= "- Recommend professional medical consultation when appropriate\n";
    $base_prompt .= "- Consider common conditions first, but don't ignore serious possibilities\n";
    $base_prompt .= "- Provide practical, actionable recommendations\n";
    $base_prompt .= "- Use clear, non-technical language that patients can understand\n";
    $base_prompt .= "- Avoid generic phrases like 'rest, stay hydrated, manage stress' unless medically necessary; if mentioned, specify amounts, timeframes, and purpose (e.g., hydration targets, dosing intervals)\n";
    $base_prompt .= "- When suggesting OTC meds, include class and typical adult dosing with caution to verify labels; avoid prescribing controlled medications\n";
    $base_prompt .= "- Include time-bound thresholds (e.g., if no improvement in 48–72h, seek care) and condition-specific red flags\n";

    return $base_prompt;
}

function parseAIResponse($response) {
    $analysis = [
        'condition' => 'Unable to determine specific condition',
        'urgency' => 'moderate',
        'recommendations' => 'Please consult with a healthcare professional for proper evaluation'
    ];

    if (empty($response)) {
        return $analysis;
    }

    // Try to extract structured information
    $lines = explode("\n", $response);

    foreach ($lines as $line) {
        $line = trim($line);

        if (stripos($line, 'Possible Condition:') === 0 || stripos($line, 'Possible Illness:') === 0) {
            $analysis['condition'] = trim(substr($line, strpos($line, ':') + 1));
        } elseif (stripos($line, 'Urgency Level:') === 0 || stripos($line, 'Urgency:') === 0) {
            $urgency_text = strtolower(trim(substr($line, strpos($line, ':') + 1)));

            if (strpos($urgency_text, 'mild') !== false || strpos($urgency_text, 'low') !== false) {
                $analysis['urgency'] = 'mild';
            } elseif (strpos($urgency_text, 'urgent') !== false || strpos($urgency_text, 'high') !== false || strpos($urgency_text, 'severe') !== false) {
                $analysis['urgency'] = 'urgent';
            } else {
                $analysis['urgency'] = 'moderate';
            }
        } elseif (stripos($line, 'Recommendations:') === 0 || stripos($line, 'Recommendation:') === 0) {
            $analysis['recommendations'] = trim(substr($line, strpos($line, ':') + 1));
        }
    }

    return $analysis;
}

function generateMockResponse($prompt) {
    // Extract symptoms from prompt for more realistic responses
    $symptoms = [];
    $input = '';

    if (preg_match('/reports: "([^"]+)"/', $prompt, $matches)) {
        $input = strtolower($matches[1]);
    } elseif (preg_match('/user reports: "([^"]+)"/', $prompt, $matches)) {
        $input = strtolower($matches[1]);
    } elseif (preg_match('/symptoms.*?:\s*(.+?)(?:\.|$)/i', $prompt, $matches)) {
        $input = strtolower($matches[1]);
    } else {
        // Fallback: extract from the entire prompt
        $input = strtolower($prompt);
    }

    // Comprehensive symptom detection
    $symptom_patterns = [
        'fever' => ['fever', 'temperature', 'hot', 'chills', 'burning up'],
        'headache' => ['headache', 'head pain', 'migraine', 'head hurt'],
        'cough' => ['cough', 'coughing', 'hack'],
        'sore_throat' => ['sore throat', 'throat pain', 'throat hurt', 'swallow'],
        'nausea' => ['nausea', 'nauseous', 'vomit', 'throw up', 'sick stomach'],
        'fatigue' => ['tired', 'fatigue', 'exhausted', 'weak', 'energy'],
        'runny_nose' => ['runny nose', 'stuffy', 'congestion', 'sneezing'],
        'body_aches' => ['body ache', 'muscle pain', 'joint pain', 'sore'],
        'diarrhea' => ['diarrhea', 'loose stool', 'stomach upset'],
        'rash' => ['rash', 'skin', 'itchy', 'red spots'],
        'chest_pain' => ['chest pain', 'chest hurt', 'breathing'],
        'dizziness' => ['dizzy', 'lightheaded', 'balance']
    ];

    foreach ($symptom_patterns as $symptom => $patterns) {
        foreach ($patterns as $pattern) {
            if (strpos($input, $pattern) !== false) {
                $symptoms[] = $symptom;
                break;
            }
        }
    }

    // Analyze symptoms and determine condition
    $analysis = analyzeSymptoms($symptoms, $input);

    // Generate structured response using the analysis
    return generateStructuredResponse($analysis, $input);
}

function analyzeSymptoms($symptoms, $input) {
    $analysis = [
        'condition' => 'Unable to determine specific condition',
        'urgency' => 'mild',
        'recommendations' => 'Monitor symptoms and consult with a healthcare professional if symptoms persist or worsen'
    ];

    if (empty($symptoms)) {
        $analysis['condition'] = 'Insufficient symptom information provided';
        $analysis['urgency'] = 'mild';
        $analysis['recommendations'] = 'Please provide more specific symptoms for a better assessment. Consider consulting with a healthcare professional.';
        return $analysis;
    }

    // Advanced symptom combination analysis
    if (in_array('fever', $symptoms) && in_array('headache', $symptoms) && in_array('body_aches', $symptoms)) {
        $analysis['condition'] = 'Viral Upper Respiratory Infection (such as the common cold or flu)';
        $analysis['urgency'] = 'moderate';
        $analysis['recommendations'] = 'Rest, stay hydrated, monitor temperature. Consider COVID-19 testing if recent exposure. Seek medical attention if symptoms worsen or persist beyond 7-10 days.';
    } elseif (in_array('fever', $symptoms) && in_array('sore_throat', $symptoms)) {
        $analysis['condition'] = 'Bacterial Throat Infection (such as strep throat)';
        $analysis['urgency'] = 'moderate';
        $analysis['recommendations'] = 'See a healthcare provider for throat culture or rapid strep test. May require antibiotic treatment if bacterial infection is confirmed.';
    } elseif (in_array('cough', $symptoms) && in_array('runny_nose', $symptoms) && in_array('fatigue', $symptoms)) {
        $analysis['condition'] = 'Viral Upper Respiratory Infection (such as the common cold)';
        $analysis['urgency'] = 'mild';
        $analysis['recommendations'] = 'Rest, increase fluid intake, use humidifier. Over-the-counter medications may help with symptoms. Consult doctor if symptoms persist beyond 10 days.';
    } elseif (in_array('nausea', $symptoms) && in_array('diarrhea', $symptoms)) {
        $analysis['condition'] = 'Gastroenteritis (stomach flu or food poisoning)';
        $analysis['urgency'] = 'moderate';
        $analysis['recommendations'] = 'Stay hydrated with clear fluids, follow BRAT diet (bananas, rice, applesauce, toast). Seek medical attention if severe dehydration, blood in stool, or symptoms persist beyond 3 days.';
    } elseif (in_array('chest_pain', $symptoms)) {
        $analysis['condition'] = 'Chest Pain - Requires Medical Evaluation';
        $analysis['urgency'] = 'urgent';
        $analysis['recommendations'] = 'Seek immediate medical attention. Chest pain can indicate serious conditions including heart problems, lung issues, or other medical emergencies.';
    } elseif (in_array('headache', $symptoms) && in_array('dizziness', $symptoms)) {
        $analysis['condition'] = 'Tension Headache or Migraine';
        $analysis['urgency'] = 'mild';
        $analysis['recommendations'] = 'Rest in a dark, quiet room. Stay hydrated. Apply cold or warm compress. Avoid known triggers. Consult healthcare provider if headaches are frequent or severe.';
    } elseif (in_array('rash', $symptoms)) {
        $analysis['condition'] = 'Allergic Reaction or Skin Condition';
        $analysis['urgency'] = 'mild';
        $analysis['recommendations'] = 'Avoid known allergens, use gentle skin products. Apply cool compress if itchy. Seek medical attention if rash spreads rapidly or is accompanied by difficulty breathing.';
    } else {
        // Single symptom analysis
        $primary_symptom = $symptoms[0];
        switch ($primary_symptom) {
            case 'fever':
                $analysis['condition'] = 'Viral or Bacterial Infection';
                $analysis['urgency'] = 'moderate';
                $analysis['recommendations'] = 'Monitor temperature, stay hydrated, rest. Seek medical attention if fever exceeds 103°F (39.4°C) or persists beyond 3 days.';
                break;
            case 'headache':
                $analysis['condition'] = 'Tension Headache';
                $analysis['urgency'] = 'mild';
                $analysis['recommendations'] = 'Rest, stay hydrated, manage stress. Over-the-counter pain relievers may help. Consult doctor if headaches are frequent or severe.';
                break;
            case 'cough':
                $analysis['condition'] = 'Upper Respiratory Irritation or Infection';
                $analysis['urgency'] = 'mild';
                $analysis['recommendations'] = 'Stay hydrated, use humidifier, avoid irritants. Consult healthcare provider if cough persists beyond 2 weeks or produces blood.';
                break;
            case 'sore_throat':
                $analysis['condition'] = 'Viral or Bacterial Throat Infection';
                $analysis['urgency'] = 'mild';
                $analysis['recommendations'] = 'Gargle with warm salt water, stay hydrated, rest. See healthcare provider if severe pain, difficulty swallowing, or symptoms persist beyond 5 days.';
                break;
            case 'fatigue':
                $analysis['condition'] = 'General Fatigue or Viral Infection';
                $analysis['urgency'] = 'mild';
                $analysis['recommendations'] = 'Ensure adequate sleep, maintain balanced diet, stay hydrated. Consult healthcare provider if fatigue persists or is accompanied by other symptoms.';
                break;
            default:
                $analysis['condition'] = 'General Health Concern';
                $analysis['urgency'] = 'mild';
                $analysis['recommendations'] = 'Monitor symptoms closely. Consult with a healthcare professional for proper evaluation and guidance.';
        }
    }

    return $analysis;
}

function generateStructuredResponse($analysis, $raw_text = '') {
    // Determine urgency color and text
    $urgency_colors = [
        'mild' => 'success',
        'moderate' => 'warning',
        'urgent' => 'danger'
    ];

    $urgency_text = [
        'mild' => 'MILD',
        'moderate' => 'MODERATE',
        'urgent' => 'URGENT'
    ];

    $urgency_color = $urgency_colors[$analysis['urgency']] ?? 'secondary';
    $urgency_display = $urgency_text[$analysis['urgency']] ?? 'MODERATE';

    // Generate the structured HTML response
    $response = '
    <div class="analysis-results">
        <h4><i class="fas fa-stethoscope"></i> Analysis Results</h4>

        <div class="result-section mb-4">
            <div class="condition-card p-3 border-start border-success border-4">
                <h5><i class="fas fa-diagnoses"></i> Possible Condition</h5>
                <p class="mb-0 text-muted">' . htmlspecialchars($analysis['condition']) . '</p>
            </div>
        </div>

        <div class="result-section mb-4">
            <div class="urgency-card p-3">
                <h5><i class="fas fa-exclamation-triangle"></i> Urgency Level</h5>
                <span class="badge bg-' . $urgency_color . ' fs-6">' . $urgency_display . '</span>
                <p class="mt-2 mb-0 small text-muted">' . getUrgencyDescription($analysis['urgency']) . '</p>
            </div>
        </div>

        <div class="result-section mb-4">
            <div class="recommendations-card p-3">
                <h5><i class="fas fa-clipboard-list"></i> Recommendations</h5>
                <p class="mb-0 text-muted">' . htmlspecialchars($analysis['recommendations']) . '</p>
            </div>
        </div>

        <div class="result-section">
            <div class="note-card p-3">
                <h5><i class="fas fa-info-circle"></i> Important Note</h5>
                <p class="mb-0 small text-muted">This analysis is for informational purposes only and should not replace professional medical advice. If you\'re experiencing severe symptoms or are concerned about your health, please consult a healthcare professional immediately.</p>
            </div>
        </div>
    </div>

    <style>
    .analysis-results {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
    }
    .result-section h5 {
        color: #2c3e50;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    .condition-card, .urgency-card, .recommendations-card {
        background: #f8f9fa;
        border-radius: 8px;
    }
    .note-card {
        border-radius: 8px;
    }
    .fas {
        margin-right: 8px;
        color: #007bff;
    }
    </style>';

    return $response;
}

function getUrgencyDescription($urgency) {
    switch ($urgency) {
        case 'mild':
            return 'Monitor symptoms and rest';
        case 'moderate':
            return 'Consider consulting a healthcare provider';
        case 'urgent':
            return 'Seek medical attention promptly';
        default:
            return 'Monitor symptoms and rest';
    }
}

function extractTextSummary($html_response) {
    // Extract text content from HTML response for dashboard display
    $text = strip_tags($html_response);
    $text = html_entity_decode($text);
    $text = preg_replace('/\s+/', ' ', $text);
    $text = trim($text);

    // Limit to first 200 characters for dashboard
    if (strlen($text) > 200) {
        $text = substr($text, 0, 200) . '...';
    }

    return $text;
}


// --- Reviews helpers ---
function ensureReviewsTable(PDO $pdo) {
    $sql = "CREATE TABLE IF NOT EXISTS reviews (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        rating TINYINT NOT NULL,
        comment TEXT NOT NULL,
        approved TINYINT(1) NOT NULL DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX (user_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    try { $pdo->exec($sql); } catch (Throwable $e) { /* ignore */ }
}

function getLatestReviews(PDO $pdo, int $limit = 3): array {
    $limit = max(1, min(12, (int)$limit));
    // Note: LIMIT cannot be bound when emulate prepares is off, so interpolate sanitized int
    $sql = "SELECT r.rating, r.comment, r.created_at, u.name
            FROM reviews r JOIN users u ON r.user_id = u.id
            WHERE r.approved = 1
            ORDER BY r.created_at DESC
            LIMIT $limit";
    try {
        $stmt = $pdo->query($sql);
        return $stmt->fetchAll() ?: [];
    } catch (Throwable $e) {
        return [];
    }
}
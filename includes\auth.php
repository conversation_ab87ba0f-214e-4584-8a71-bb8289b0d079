<?php
// Authentication and session management for Health Oracle
session_start();

function is_logged_in() {
    return isset($_SESSION['user_id']);
}

function is_admin() {
    return (isset($_SESSION['role']) && $_SESSION['role'] === 'admin');
}

function require_login() {
    if (!is_logged_in()) {
        header('Location: /health-oracle/login.php');
        exit();
    }
}

function require_admin() {
    if (!is_admin()) {
        header('Location: /health-oracle/index.php');
        exit();
    }
}

function generate_csrf_token() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

function verify_csrf_token($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

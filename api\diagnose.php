<?php
// Endpoint for AI-powered diagnosis
header('Content-Type: application/json');
require_once __DIR__ . '/../includes/db.php';
require_once __DIR__ . '/../includes/functions.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

$data = json_decode(file_get_contents('php://input'), true);
$symptoms = sanitize_input($data['symptoms'] ?? '');
$user_id = $data['user_id'] ?? null;
$guest_ip = $data['guest_ip'] ?? null;

if (!$symptoms) {
    http_response_code(400);
    echo json_encode(['error' => 'Symptoms required']);
    exit;
}

// Compose prompt
$prompt = "You are a professional medical assistant. A user reports: \"$symptoms\". Suggest possible illnesses and appropriate next steps in a clear, professional tone. Do not give emergency or life-threatening suggestions. Always recommend seeing a real doctor.";

// Call OpenAI API
require_once __DIR__ . '/../includes/config.php';
$ai_response = callOpenAI($prompt);

// Store in DB
$pdo->prepare('INSERT INTO diagnoses (user_id, guest_ip, input_text, ai_response) VALUES (?, ?, ?, ?)')
    ->execute([$user_id, $guest_ip, $symptoms, $ai_response]);

echo json_encode(['result' => $ai_response]);

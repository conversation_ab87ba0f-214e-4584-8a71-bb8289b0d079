<?php
// Auto-redirect to login if accessing admin directory directly
require_once __DIR__ . '/../includes/auth.php';

// Check if user is logged in and is admin
if (!is_logged_in()) {
    // Not logged in, redirect to login
    header('Location: /health-oracle/login.php');
    exit();
} elseif (!is_admin()) {
    // Logged in but not admin, redirect to user dashboard
    header('Location: /health-oracle/dashboard.php');
    exit();
} else {
    // Logged in and is admin, redirect to admin dashboard
    header('Location: /health-oracle/admin/dashboard.php');
    exit();
}
?>

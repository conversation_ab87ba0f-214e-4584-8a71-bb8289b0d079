<?php
require_once __DIR__ . '/includes/db.php';
require_once __DIR__ . '/includes/functions.php';

$ip = $_SERVER['REMOTE_ADDR'];
$error = '';
$can_diagnose = true;

// Get guest usage limit from settings
$limit = $pdo->query('SELECT guest_limit FROM settings WHERE id=1')->fetchColumn();
if (!$limit) $limit = 3;

// Get guest usage count
$guest = $pdo->prepare('SELECT * FROM guests WHERE ip_address = ?');
$guest->execute([$ip]);
$guest = $guest->fetch();
$usage_count = $guest ? $guest['usage_count'] : 0;

if ($usage_count >= $limit) {
    $can_diagnose = false;
    $error = 'You have reached the daily guest usage limit.';
}
?><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Symptom Checker - Health Oracle</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&family=Inter:wght@400;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
    <style>
        body {
            font-family: 'Poppins', 'Inter', sans-serif;
            background: linear-gradient(135deg, #E8F4FD 0%, #F0F8FF 50%, #E6F3FF 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px 0;
        }

        .main-container {
            max-width: 1500px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .header-section {
            text-align: center;
            margin-bottom: 2rem;
        }

        .header-icon {
            font-size: 3rem;
            color: #4A90E2;
            margin-bottom: 1rem;
        }

        .main-title {
            color: #2C5282;
            font-weight: 700;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .subtitle {
            color: #718096;
            font-size: 1.1rem;
            margin-bottom: 2rem;
        }

        .symptom-input-card {
            background: linear-gradient(135deg, #5B9BD5 0%, #4A90E2 100%);
            border-radius: 1rem;
            padding: 0;
            margin-bottom: 2rem;
            box-shadow: 0 8px 32px rgba(74, 144, 226, 0.2);

        }

        .symptom-input-header {
            color: white;
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            padding: 1.5rem 2rem 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .symptom-input-subtitle {
            color: rgba(255, 255, 255, 0.95);
            margin-bottom: 0;
            padding: 0 2rem 1.5rem;
            text-align: center;
            font-size: 0.95rem;
        }

        .input-section {
            background: white;
            border-radius: 0;
            border-bottom-left-radius: 1rem;
            border-bottom-right-radius: 1rem;
            padding: 2rem;
            margin: 0;
        }

        .toggle-btns {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 2rem;
            justify-content: center;
        }

        .toggle-btns .btn {
            border-radius: 2rem;
            font-weight: 500;
            padding: 0.6rem 1.5rem;
            border: 1px solid #E2E8F0;
            background: #F8F9FA;
            color: #6C757D;
            font-size: 0.9rem;
        }

        .toggle-btns .btn.active {
            background: #5B9BD5;
            color: white;
            border-color: #5B9BD5;
        }

        .form-group label {
            font-weight: 600;
            color: #2D3748;
            margin-bottom: 0.75rem;
            display: block;
            font-size: 0.95rem;
        }

        .form-control {
            border-radius: 0.5rem;
            border: 1px solid #E2E8F0;
            padding: 1rem;
            font-size: 0.95rem;
            min-height: 120px;
            transition: all 0.2s;
        }

        .form-control:focus {
            border-color: #5B9BD5;
            box-shadow: 0 0 0 3px rgba(91, 155, 213, 0.1);
            outline: none;
        }

        .example-text {
            font-size: 0.85rem;
            color: #718096;
            margin-bottom: 1rem;
        }

        .analyze-btn {
            background: linear-gradient(135deg, #5B9BD5 0%, #4A90E2 100%);
            border: none;
            border-radius: 2rem;
            padding: 0.8rem 2.5rem;
            font-weight: 600;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            margin: 2rem auto 0;
            font-size: 0.95rem;
        }

        .analyze-btn:hover {
            background: linear-gradient(135deg, #357ABD 0%, #2C5282 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
        }

        .privacy-notice {
            font-size: 0.85rem;
            color: #5B9BD5;
            background: rgba(91, 155, 213, 0.08);
            border-radius: 0.5rem;
            padding: 0.75rem 1rem;
            margin-top: 1.5rem;
            border-left: 3px solid #5B9BD5;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-top: 3rem;
        }

        .feature-card {
            background: white;
            border-radius: 0.75rem;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            border: 1px solid #F1F5F9;
        }

        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .feature-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.25rem;
        }

        .feature-icon.analysis {
            background: #5B9BD5;
            color: white;
        }

        .feature-icon.results {
            background: #5B9BD5;
            color: white;
        }

        .feature-icon.guidance {
            background: #5B9BD5;
            color: white;
        }

        .feature-title {
            font-weight: 600;
            color: #2D3748;
            margin-bottom: 0.75rem;
            font-size: 1rem;
        }

        .feature-description {
            color: #64748B;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .auth-links {
            text-align: center;
            margin-top: 2rem;
            color: #718096;
        }

        .auth-links a {
            color: #4A90E2;
            text-decoration: none;
            font-weight: 500;
        }

        .auth-links a:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .main-title {
                font-size: 2rem;
            }

            .symptom-input-card {
                padding: 1.5rem;
            }

            .input-section {
                padding: 1rem;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
<?= getNavbarHtml() ?>
    <div class="main-container">
        <!-- Header Section -->
        <div class="header-section">
            <div class="header-icon">🩺</div>
            <h1 class="main-title">AI Symptom Checker</h1>
            <p class="subtitle">Describe your symptoms and get AI-powered health insights with<br>recommended next steps</p>
        </div>

        <!-- Symptom Input Card -->
        <div class="symptom-input-card">
            <div class="symptom-input-header text-center">
                🔒 Symptom Input
            </div>
            <p class="symptom-input-subtitle">Choose how you'd like to describe your symptoms for the most accurate analysis</p>

            <div class="input-section">
                <div class="toggle-btns">
                    <button class="btn active" id="btn-keywords" type="button">Quick Keywords</button>
                    <button class="btn" id="btn-description" type="button">Detailed Description</button>
                </div>

                <?php if ($error): ?>
                    <div class="alert alert-danger mb-3"> <?= htmlspecialchars($error) ?> </div>
                <?php endif; ?>

                <?php if ($can_diagnose): ?>
                <form method="post" action="/health-oracle/result.php" id="symptom-form">
                    <div id="keywords-input">
                        <div class="form-group">
                            <label>Enter symptom keywords (separated by commas)</label>
                            <p class="example-text">Example: fever, cough, sore throat, headache</p>
                            <input type="text" class="form-control" name="symptoms" placeholder="fever, cough, sore throat..." required>
                        </div>
                    </div>
                    <div id="description-input" style="display:none;">
                        <div class="form-group">
                            <label>Describe your symptoms in detail</label>
                            <textarea class="form-control" name="symptoms" rows="4" placeholder="Example: I've had a fever and sore throat for 2 days, and now I have a headache."></textarea>
                        </div>
                    </div>
                    <input type="hidden" name="guest" value="1">
                    <button type="submit" class="analyze-btn">
                        🔍 Analyze Symptoms
                    </button>
                </form>
                <?php endif; ?>

                <div class="privacy-notice">
                    <strong>Privacy Notice:</strong> Your symptom data is processed securely and not stored permanently. This tool provides general health information and should not replace professional medical consultation.
                </div>
            </div>
        </div>
        <!-- Features Grid -->
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon analysis">
                    📊
                </div>
                <h6 class="feature-title">AI-Powered Analysis</h6>
                <div class="feature-description">Advanced AI analyzes symptoms for accurate health insights</div>
            </div>
            <div class="feature-card">
                <div class="feature-icon results">
                    ⚡
                </div>
                <h6 class="feature-title">Instant Results</h6>
                <div class="feature-description">Get immediate analysis with urgency levels and recommendations</div>
            </div>
            <div class="feature-card">
                <div class="feature-icon guidance">
                    ✅
                </div>
                <h6 class="feature-title">Actionable Guidance</h6>
                <div class="feature-description">Clear next steps and recommendations for your health concerns</div>
            </div>
        </div>

        <div class="auth-links">
            <a href="/health-oracle/login.php">Login</a> or <a href="/health-oracle/register.php">Register</a> for more features.
        </div>
    </div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
<script>
// Toggle input modes
const btnKeywords = document.getElementById('btn-keywords');
const btnDescription = document.getElementById('btn-description');
const keywordsInput = document.getElementById('keywords-input');
const descriptionInput = document.getElementById('description-input');
btnKeywords.addEventListener('click', function() {
    btnKeywords.classList.add('active');
    btnDescription.classList.remove('active');
    keywordsInput.style.display = '';
    descriptionInput.style.display = 'none';
    keywordsInput.querySelector('input').required = true;
    descriptionInput.querySelector('textarea').required = false;
});
btnDescription.addEventListener('click', function() {
    btnDescription.classList.add('active');
    btnKeywords.classList.remove('active');
    keywordsInput.style.display = 'none';
    descriptionInput.style.display = '';
    keywordsInput.querySelector('input').required = false;
    descriptionInput.querySelector('textarea').required = true;
});
</script>
</body>
</html>

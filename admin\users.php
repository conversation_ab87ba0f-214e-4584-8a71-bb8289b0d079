<?php
require_once __DIR__ . '/../includes/auth.php';
require_admin();
require_once __DIR__ . '/../includes/db.php';
require_once __DIR__ . '/../includes/functions.php';

$success = '';
$error = '';

// Handle manual user upgrade
if ($_POST && isset($_POST['upgrade_user'])) {
    $user_id = (int)$_POST['user_id'];
    $plan = sanitize_input($_POST['plan']);
    $duration = (int)$_POST['duration'];

    if ($user_id && $plan && $duration) {
        $expires_date = date('Y-m-d', strtotime("+{$duration} days"));

        $stmt = $pdo->prepare("UPDATE users SET subscription_plan = ?, subscription_expires = ? WHERE id = ?");
        if ($stmt->execute([$plan, $expires_date, $user_id])) {
            // Log the manual upgrade
            $stmt = $pdo->prepare('INSERT INTO payments (user_id, plan_name, amount, reference_code, payment_method, status) VALUES (?, ?, ?, ?, ?, ?)');
            $stmt->execute([$user_id, $plan, 0, 'ADMIN_UPGRADE_' . time(), 'Manual', 'Approved']);

            $success = "User successfully upgraded to {$plan} plan for {$duration} days.";
        } else {
            $error = "Failed to upgrade user.";
        }
    } else {
        $error = "Invalid upgrade parameters.";
    }
}

// Handle user role change
if ($_POST && isset($_POST['change_role'])) {
    $user_id = (int)$_POST['user_id'];
    $new_role = sanitize_input($_POST['new_role']);

    if ($user_id && in_array($new_role, ['user', 'admin'])) {
        $stmt = $pdo->prepare("UPDATE users SET role = ? WHERE id = ?");
        if ($stmt->execute([$new_role, $user_id])) {
            $success = "User role updated successfully.";
        } else {
            $error = "Failed to update user role.";
        }
    }
}

$users = $pdo->query('SELECT * FROM users ORDER BY date_registered DESC')->fetchAll();
?><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Users | Health Oracle</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="/assets/css/style.css">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        .admin-card {
            border-radius: 1.5rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            background: #fff;
            border: none;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 1rem;
            border: none;
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }
        .badge-role {
            font-size: 0.75rem;
            padding: 0.5rem 0.75rem;
        }
        .table th {
            background: #f8f9fa;
            border: none;
            font-weight: 600;
            color: #495057;
        }
        .table td {
            border: none;
            vertical-align: middle;
            padding: 1rem 0.75rem;
        }
        .table tbody tr {
            border-bottom: 1px solid #e9ecef;
        }
        .table tbody tr:hover {
            background: #f8f9fa;
        }
        .btn-action {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
            border-radius: 0.5rem;
        }
        .subscription-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
    </style>
</head>
<body>
<?= getNavbarHtml() ?>
<div class="container py-5">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 mb-1" style="color:#495057;"><i class="bi bi-people-fill"></i> User Management</h1>
                    <p class="text-muted mb-0">Manage user accounts, subscriptions, and permissions</p>
                </div>
                <div class="stats-card p-3">
                    <div class="text-center">
                        <div class="h4 mb-0"><?= count($users) ?></div>
                        <small>Total Users</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alerts -->
    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle"></i> <?= htmlspecialchars($success) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle"></i> <?= htmlspecialchars($error) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Users Table -->
    <div class="admin-card p-0">
        <div class="p-4 border-bottom">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-table"></i> All Users</h5>
                <div class="d-flex gap-2">
                    <input type="text" class="form-control form-control-sm" placeholder="Search users..." id="userSearch" style="width: 200px;">
                    <button class="btn btn-outline-primary btn-sm" onclick="exportUsers()">
                        <i class="bi bi-download"></i> Export
                    </button>
                </div>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-hover mb-0" id="usersTable">
                <thead>
                    <tr>
                        <th>User</th>
                        <th>Role</th>
                        <th>Subscription</th>
                        <th>Usage</th>
                        <th>Last Active</th>
                        <th>Registered</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($users as $u):
                        $subscription_status = 'Free';
                        $subscription_class = 'secondary';
                        if ($u['subscription_plan'] && $u['subscription_plan'] !== 'free') {
                            if ($u['subscription_expires'] && $u['subscription_expires'] >= date('Y-m-d')) {
                                $subscription_status = ucfirst($u['subscription_plan']);
                                $subscription_class = $u['subscription_plan'] === 'pro' ? 'success' : 'primary';
                            } else {
                                $subscription_status = 'Expired';
                                $subscription_class = 'warning';
                            }
                        }
                    ?>
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="user-avatar me-3">
                                    <?= strtoupper(substr($u['name'], 0, 1)) ?>
                                </div>
                                <div>
                                    <div class="fw-semibold"><?= htmlspecialchars($u['name']) ?></div>
                                    <small class="text-muted"><?= htmlspecialchars($u['email']) ?></small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge badge-role bg-<?= $u['role'] === 'admin' ? 'danger' : 'info' ?>">
                                <i class="bi bi-<?= $u['role'] === 'admin' ? 'shield-check' : 'person' ?>"></i>
                                <?= ucfirst($u['role']) ?>
                            </span>
                        </td>
                        <td>
                            <span class="badge subscription-badge bg-<?= $subscription_class ?>">
                                <?= $subscription_status ?>
                            </span>
                            <?php if ($u['subscription_expires'] && $u['subscription_expires'] >= date('Y-m-d')): ?>
                                <br><small class="text-muted">Until <?= date('M d, Y', strtotime($u['subscription_expires'])) ?></small>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div class="text-center">
                                <div class="fw-semibold"><?= (int)$u['usage_count'] ?></div>
                                <small class="text-muted">diagnoses</small>
                            </div>
                        </td>
                        <td>
                            <?php if ($u['last_usage_date']): ?>
                                <span class="text-muted"><?= date('M d, Y', strtotime($u['last_usage_date'])) ?></span>
                            <?php else: ?>
                                <span class="text-muted">Never</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <span class="text-muted"><?= date('M d, Y', strtotime($u['date_registered'])) ?></span>
                        </td>
                        <td>
                            <div class="d-flex gap-1">
                                <button class="btn btn-outline-primary btn-action"
                                        onclick="openUpgradeModal(<?= $u['id'] ?>, '<?= htmlspecialchars($u['name']) ?>', '<?= $u['subscription_plan'] ?>')"
                                        title="Upgrade User">
                                    <i class="bi bi-arrow-up-circle"></i>
                                </button>
                                <button class="btn btn-outline-secondary btn-action"
                                        onclick="openRoleModal(<?= $u['id'] ?>, '<?= htmlspecialchars($u['name']) ?>', '<?= $u['role'] ?>')"
                                        title="Change Role">
                                    <i class="bi bi-person-gear"></i>
                                </button>
                                <button class="btn btn-outline-info btn-action"
                                        onclick="viewUserDetails(<?= $u['id'] ?>)"
                                        title="View Details">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<!-- Upgrade User Modal -->
<div class="modal fade" id="upgradeModal" tabindex="-1" aria-labelledby="upgradeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="upgradeModalLabel">
                    <i class="bi bi-arrow-up-circle"></i> Upgrade User
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="post">
                <div class="modal-body">
                    <input type="hidden" name="user_id" id="upgradeUserId">

                    <div class="mb-3">
                        <label class="form-label">User</label>
                        <input type="text" class="form-control" id="upgradeUserName" readonly>
                    </div>

                    <div class="mb-3">
                        <label for="plan" class="form-label">Subscription Plan</label>
                        <select class="form-select" name="plan" required>
                            <option value="">Select plan...</option>
                            <option value="basic">Basic ($10/month)</option>
                            <option value="pro">Pro ($25/month)</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="duration" class="form-label">Duration (Days)</label>
                        <select class="form-select" name="duration" required>
                            <option value="">Select duration...</option>
                            <option value="30">30 Days (1 Month)</option>
                            <option value="90">90 Days (3 Months)</option>
                            <option value="180">180 Days (6 Months)</option>
                            <option value="365">365 Days (1 Year)</option>
                        </select>
                    </div>

                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i> This will manually upgrade the user's subscription. A payment record will be created for tracking.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="upgrade_user" class="btn btn-primary">
                        <i class="bi bi-check"></i> Upgrade User
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Change Role Modal -->
<div class="modal fade" id="roleModal" tabindex="-1" aria-labelledby="roleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="roleModalLabel">
                    <i class="bi bi-person-gear"></i> Change User Role
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post">
                <div class="modal-body">
                    <input type="hidden" name="user_id" id="roleUserId">

                    <div class="mb-3">
                        <label class="form-label">User</label>
                        <input type="text" class="form-control" id="roleUserName" readonly>
                    </div>

                    <div class="mb-3">
                        <label for="new_role" class="form-label">New Role</label>
                        <select class="form-select" name="new_role" id="newRole" required>
                            <option value="">Select role...</option>
                            <option value="user">User</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>

                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i> <strong>Warning:</strong> Changing a user to admin will give them full access to the admin panel.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="change_role" class="btn btn-warning">
                        <i class="bi bi-check"></i> Change Role
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<script>
// Upgrade user modal
function openUpgradeModal(userId, userName, currentPlan) {
    document.getElementById('upgradeUserId').value = userId;
    document.getElementById('upgradeUserName').value = userName;
    new bootstrap.Modal(document.getElementById('upgradeModal')).show();
}

// Change role modal
function openRoleModal(userId, userName, currentRole) {
    document.getElementById('roleUserId').value = userId;
    document.getElementById('roleUserName').value = userName;
    document.getElementById('newRole').value = currentRole;
    new bootstrap.Modal(document.getElementById('roleModal')).show();
}

// View user details (placeholder)
function viewUserDetails(userId) {
    // This could open a detailed modal or redirect to a user details page
    alert('User details functionality - to be implemented');
}

// Export users (placeholder)
function exportUsers() {
    // This could export to CSV or PDF
    alert('Export functionality - to be implemented');
}

// Search functionality
document.getElementById('userSearch').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const tableRows = document.querySelectorAll('#usersTable tbody tr');

    tableRows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchTerm) ? '' : 'none';
    });
});

// Auto-dismiss alerts after 5 seconds
setTimeout(() => {
    const alerts = document.querySelectorAll('.alert-dismissible');
    alerts.forEach(alert => {
        const bsAlert = new bootstrap.Alert(alert);
        bsAlert.close();
    });
}, 5000);
</script>
</body>
</html>

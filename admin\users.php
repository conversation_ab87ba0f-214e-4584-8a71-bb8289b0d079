<?php
require_once __DIR__ . '/../includes/auth.php';
require_admin();
require_once __DIR__ . '/../includes/db.php';
require_once __DIR__ . '/../includes/functions.php';

$users = $pdo->query('SELECT * FROM users ORDER BY date_registered DESC')->fetchAll();
?><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Users | Health Oracle</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&family=Inter:wght@400;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
    <style>
        body { font-family: 'Poppins', 'Inter', sans-serif; background: #F8F9FA; }
        .admin-card { border-radius: 1rem; box-shadow: 0 2px 16px rgba(70,130,180,0.08); background: #fff; }
    </style>
</head>
<body>
<?= getNavbarHtml() ?>
<div class="container py-5">
    <div class="admin-card p-4">
        <h2 class="mb-4" style="color:#007BFF;">All Users</h2>
        <div class="table-responsive">
        <table class="table table-bordered">
            <thead>
                <tr><th>Name</th><th>Email</th><th>Role</th><th>Usage</th><th>Last Usage</th><th>Registered</th></tr>
            </thead>
            <tbody>
                <?php foreach ($users as $u): ?>
                <tr>
                    <td><?= htmlspecialchars($u['name']) ?></td>
                    <td><?= htmlspecialchars($u['email']) ?></td>
                    <td><?= htmlspecialchars($u['role']) ?></td>
                    <td><?= (int)$u['usage_count'] ?></td>
                    <td><?= htmlspecialchars($u['last_usage_date']) ?></td>
                    <td><?= htmlspecialchars($u['date_registered']) ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        </div>
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

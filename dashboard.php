<?php
require_once __DIR__ . '/includes/auth.php';
require_login();
require_once __DIR__ . '/includes/db.php';

$user_id = $_SESSION['user_id'];
$name = isset($_SESSION['name']) ? $_SESSION['name'] : '';
$role = isset($_SESSION['role']) ? $_SESSION['role'] : '';

// Fetch user stats
$stmt = $pdo->prepare('SELECT usage_count, subscription_plan, subscription_expires FROM users WHERE id = ?');
$stmt->execute([$user_id]);
$user_data = $stmt->fetch();
$usage_count = $user_data['usage_count'];

// Check subscription status
$subscription_status = 'Free (5 diagnoses/day)';
if ($user_data['subscription_plan'] && $user_data['subscription_plan'] !== 'free') {
    if ($user_data['subscription_expires'] && $user_data['subscription_expires'] >= date('Y-m-d')) {
        if ($user_data['subscription_plan'] === 'basic') {
            $subscription_status = 'Basic (50 diagnoses/day)';
        } elseif ($user_data['subscription_plan'] === 'pro') {
            $subscription_status = 'Pro (Unlimited)';
        }
    } else {
        $subscription_status = 'Expired - Please renew';
    }
}

// Handle diagnosis deletion
if ($_POST && isset($_POST['delete_diagnosis'])) {
    $diagnosis_id = (int)$_POST['diagnosis_id'];
    $stmt = $pdo->prepare('DELETE FROM diagnoses WHERE id = ? AND user_id = ?');
    $stmt->execute([$diagnosis_id, $user_id]);
    header('Location: /health-oracle/dashboard.php');
    exit();
}

$diagnoses = $pdo->prepare('SELECT id, timestamp, input_text, text_summary, ai_response FROM diagnoses WHERE user_id = ? ORDER BY timestamp DESC LIMIT 5');
$diagnoses->execute([$user_id]);
$diagnoses = $diagnoses->fetchAll();

require_once __DIR__ . '/includes/functions.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Health Oracle</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&family=Inter:wght@400;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
    <style>
        body { font-family: 'Poppins', 'Inter', sans-serif; background: #F8F9FA; }
        .dashboard-card { border-radius: 1rem; box-shadow: 0 2px 16px rgba(70,130,180,0.08); background: #fff; }
        .btn-primary { background: #007BFF; border: none; }
        .btn-primary:hover { background: #1E90FF; }
        .navbar-toggler { border: 1px solid #333; }
        .navbar-toggler-icon { background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2833, 37, 41, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e"); }
    </style>
</head>
<body>
<?= getNavbarHtml() ?>
<div class="container py-5">
    <div class="dashboard-card p-4 mb-4">
        <h2 class="mb-3" style="color:#007BFF;">Welcome, <?= htmlspecialchars($name) ?>!</h2>
        <p>Your daily usage: <strong><?= (int)$usage_count ?></strong></p>
        <p>Subscription: <strong><?= $subscription_status ?></strong></p>

        <div class="row mt-4">
            <div class="col-md-6">
                <div class="quick-action-card p-3 mb-3" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 1rem; color: white;">
                    <h5 class="mb-2">🩺 New Diagnosis</h5>
                    <p class="mb-3 small">Get AI-powered health insights</p>
                    <a href="/health-oracle/diagnose.php" class="btn btn-light btn-sm">Start Diagnosis</a>
                </div>
            </div>
            <div class="col-md-6">
                <div class="quick-action-card p-3 mb-3" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); border-radius: 1rem; color: white;">
    <div class="container mt-3">
        <?= renderConfigBanner() ?>
    </div>

                    <h5 class="mb-2">👤 Profile</h5>
                    <p class="mb-3 small">Manage your account settings</p>
                    <a href="/health-oracle/profile.php" class="btn btn-light btn-sm">View Profile</a>
                </div>
            </div>
        </div>
    </div>
    <div class="dashboard-card p-4">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h4 class="mb-0" style="color:#4682B4;">Recent Diagnoses</h4>
            <a href="/health-oracle/history.php" class="btn btn-sm btn-outline-primary">View All History</a>
        </div>
        <?php if ($diagnoses): ?>
            <div class="table-responsive">
            <table class="table table-bordered">
                <thead><tr><th>Date</th><th>Input</th><th>Result</th><th>Action</th></tr></thead>
                <tbody>
                <?php foreach ($diagnoses as $d): ?>
                    <tr>
                        <td><?= date('M d, Y H:i', strtotime($d['timestamp'])) ?></td>
                        <td><?= htmlspecialchars($d['input_text']) ?></td>
                        <td><?= $d['text_summary'] ? htmlspecialchars($d['text_summary']) : 'Processing...' ?></td>
                        <td>
                            <button type="button" class="btn btn-sm btn-outline-primary me-1"
                                    data-bs-toggle="modal" data-bs-target="#diagnosisModal"
                                    data-input="<?= htmlspecialchars($d['input_text'], ENT_QUOTES) ?>"
                                    data-result="<?= htmlspecialchars($d['ai_response'], ENT_QUOTES) ?>"
                                    data-date="<?= date('M d, Y H:i', strtotime($d['timestamp'])) ?>"
                                    onclick="showDiagnosis(this)">
                                👁️ View
                            </button>
                            <form method="post" style="display: inline;">
                                <input type="hidden" name="diagnosis_id" value="<?= $d['id'] ?>">
                                <button type="submit" name="delete_diagnosis" class="btn btn-sm btn-outline-danger"
                                        onclick="return confirm('Are you sure you want to delete this diagnosis?')">
                                    🗑️ Delete
                                </button>
                            </form>
                        </td>
                    </tr>
                <?php endforeach; ?>
                </tbody>
            </table>
            </div>
        <?php else: ?>
            <p>No diagnoses yet. <a href="/health-oracle/diagnose.php">Start your first diagnosis</a></p>
        <?php endif; ?>
    </div>
</div>

<!-- Diagnosis Modal -->
<div class="modal fade" id="diagnosisModal" tabindex="-1" aria-labelledby="diagnosisModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                <h5 class="modal-title" id="diagnosisModalLabel">🩺 Diagnosis Details</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <h6 class="text-primary">📅 Date & Time:</h6>
                    <p id="modalDate" class="mb-3"></p>
                </div>
                <div class="mb-3">
                    <h6 class="text-primary">📝 Your Input:</h6>
                    <div id="modalInput" class="p-3 bg-light rounded mb-3"></div>
                </div>
                <div class="mb-3">
                    <h6 class="text-primary">🎯 AI Analysis:</h6>
                    <div id="modalResult" class="p-3 border rounded"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

<script>
function showDiagnosis(button) {
    // Get data from button attributes
    const input = button.getAttribute('data-input');
    const result = button.getAttribute('data-result');
    const date = button.getAttribute('data-date');

    // Decode HTML entities including &#039; and others
    function decodeHtml(html) {
        var txt = document.createElement("textarea");
        txt.innerHTML = html;
        var decoded = txt.value;
        // Fix specific entities that might not decode properly
        decoded = decoded.replace(/&#039;/g, "'");
        decoded = decoded.replace(/&quot;/g, '"');
        decoded = decoded.replace(/&amp;/g, '&');
        decoded = decoded.replace(/&lt;/g, '<');
        decoded = decoded.replace(/&gt;/g, '>');
        return decoded;
    }

    // Set modal content
    document.getElementById('modalDate').textContent = date;
    document.getElementById('modalInput').textContent = decodeHtml(input);
    document.getElementById('modalResult').innerHTML = decodeHtml(result);
}
</script>

</body>
</html>

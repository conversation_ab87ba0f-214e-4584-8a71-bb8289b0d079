<?php
require_once __DIR__ . '/includes/auth.php';
require_login();
require_once __DIR__ . '/includes/db.php';

$user_id = $_SESSION['user_id'];
$name = isset($_SESSION['name']) ? $_SESSION['name'] : '';
$role = isset($_SESSION['role']) ? $_SESSION['role'] : '';

// Fetch user stats
$stmt = $pdo->prepare('SELECT usage_count, subscription_plan, subscription_expires FROM users WHERE id = ?');
$stmt->execute([$user_id]);
$user_data = $stmt->fetch();
$usage_count = $user_data['usage_count'];

// Check subscription status
$subscription_status = 'Free (5 diagnoses/day)';
if ($user_data['subscription_plan'] && $user_data['subscription_plan'] !== 'free') {
    if ($user_data['subscription_expires'] && $user_data['subscription_expires'] >= date('Y-m-d')) {
        if ($user_data['subscription_plan'] === 'basic') {
            $subscription_status = 'Basic (50 diagnoses/day)';
        } elseif ($user_data['subscription_plan'] === 'pro') {
            $subscription_status = 'Pro (Unlimited)';
        }
    } else {
        $subscription_status = 'Expired - Please renew';
    }
}

// Handle diagnosis deletion
if ($_POST && isset($_POST['delete_diagnosis'])) {
    $diagnosis_id = (int)$_POST['diagnosis_id'];
    $stmt = $pdo->prepare('DELETE FROM diagnoses WHERE id = ? AND user_id = ?');
    $stmt->execute([$diagnosis_id, $user_id]);
    header('Location: /health-oracle/dashboard.php');
    exit();
}

$diagnoses = $pdo->prepare('SELECT id, timestamp, input_text, text_summary, ai_response FROM diagnoses WHERE user_id = ? ORDER BY timestamp DESC LIMIT 5');
$diagnoses->execute([$user_id]);
$diagnoses = $diagnoses->fetchAll();

require_once __DIR__ . '/includes/functions.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Health Oracle</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&family=Inter:wght@400;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="/assets/css/style.css">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        .dashboard-card {
            border-radius: 1.5rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            background: #fff;
            border: none;
            transition: transform 0.3s ease;
        }
        .dashboard-card:hover {
            transform: translateY(-2px);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 1rem;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 1.5rem;
            border: none;
            position: relative;
            overflow: hidden;
        }
        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            transform: translate(30px, -30px);
        }
        .welcome-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 2rem;
            position: relative;
            overflow: hidden;
        }
        .welcome-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 200px;
            height: 200px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            transform: translate(50px, -50px);
        }
        .diagnosis-card {
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
            border-radius: 1rem;
        }
        .diagnosis-card:hover {
            background: #f8f9fa;
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .quick-action-card {
            border: 2px dashed #dee2e6;
            border-radius: 1rem;
            transition: all 0.3s ease;
            text-decoration: none;
        }
        .quick-action-card:hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
            text-decoration: none;
        }
    </style>
</head>
<body>
<?= getNavbarHtml() ?>
<div class="container py-5">
    <!-- Welcome Header -->
    <div class="welcome-card p-5 mb-5 position-relative">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-6 fw-bold mb-3">
                    <i class="bi bi-person-circle me-3"></i>Welcome back, <?= htmlspecialchars($name) ?>!
                </h1>
                <p class="lead mb-0 opacity-90">Ready to check your health? Let's get started with your diagnosis.</p>
            </div>
            <div class="col-lg-4 text-end">
                <div class="stats-card p-4 d-inline-block">
                    <div class="h3 mb-1"><?= (int)$usage_count ?></div>
                    <div class="small opacity-75">Diagnoses Today</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Configuration Banner -->
    <div class="mb-4"><?= renderConfigBanner() ?></div>

    <!-- Stats Row -->
    <div class="row g-4 mb-5">
        <div class="col-md-4">
            <div class="dashboard-card p-4 text-center">
                <div class="h2 text-primary mb-2">
                    <i class="bi bi-activity"></i>
                </div>
                <div class="h4 mb-1"><?= (int)$usage_count ?></div>
                <div class="text-muted">Total Diagnoses</div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="dashboard-card p-4 text-center">
                <div class="h2 text-success mb-2">
                    <i class="bi bi-shield-check"></i>
                </div>
                <div class="h6 mb-1"><?= $subscription_status ?></div>
                <div class="text-muted">Subscription Plan</div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="dashboard-card p-4 text-center">
                <div class="h2 text-info mb-2">
                    <i class="bi bi-clock-history"></i>
                </div>
                <div class="h6 mb-1"><?= count($diagnoses) ?></div>
                <div class="text-muted">Recent Records</div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row g-4 mb-5">
        <div class="col-md-6">
            <a href="/health-oracle/diagnose.php" class="quick-action-card p-4 d-block text-decoration-none">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                            <i class="bi bi-stethoscope fs-4"></i>
                        </div>
                    </div>
                    <div>
                        <h5 class="mb-1 text-dark">New Diagnosis</h5>
                        <p class="mb-0 text-muted">Get AI-powered health insights for your symptoms</p>
                    </div>
                </div>
            </a>
        </div>
        <div class="col-md-6">
            <a href="/health-oracle/profile.php" class="quick-action-card p-4 d-block text-decoration-none">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                            <i class="bi bi-person-gear fs-4"></i>
                        </div>
                    </div>
                    <div>
                        <h5 class="mb-1 text-dark">Manage Profile</h5>
                        <p class="mb-0 text-muted">Update your account settings and subscription</p>
                    </div>
                </div>
            </a>
        </div>
    </div>
    <div class="dashboard-card p-4">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h4 class="mb-0" style="color:#4682B4;">Recent Diagnoses</h4>
            <a href="/health-oracle/history.php" class="btn btn-sm btn-outline-primary">View All History</a>
        </div>
        <?php if ($diagnoses): ?>
            <div class="table-responsive">
            <table class="table table-bordered">
                <thead><tr><th>Date</th><th>Input</th><th>Result</th><th>Action</th></tr></thead>
                <tbody>
                <?php foreach ($diagnoses as $d): ?>
                    <tr>
                        <td><?= date('M d, Y H:i', strtotime($d['timestamp'])) ?></td>
                        <td><?= htmlspecialchars($d['input_text']) ?></td>
                        <td><?= $d['text_summary'] ? htmlspecialchars($d['text_summary']) : 'Processing...' ?></td>
                        <td>
                            <button type="button" class="btn btn-sm btn-outline-primary me-1"
                                    data-bs-toggle="modal" data-bs-target="#diagnosisModal"
                                    data-input="<?= htmlspecialchars($d['input_text'], ENT_QUOTES) ?>"
                                    data-result="<?= htmlspecialchars($d['ai_response'], ENT_QUOTES) ?>"
                                    data-date="<?= date('M d, Y H:i', strtotime($d['timestamp'])) ?>"
                                    onclick="showDiagnosis(this)">
                                👁️ View
                            </button>
                            <form method="post" style="display: inline;">
                                <input type="hidden" name="diagnosis_id" value="<?= $d['id'] ?>">
                                <button type="submit" name="delete_diagnosis" class="btn btn-sm btn-outline-danger"
                                        onclick="return confirm('Are you sure you want to delete this diagnosis?')">
                                    🗑️ Delete
                                </button>
                            </form>
                        </td>
                    </tr>
                <?php endforeach; ?>
                </tbody>
            </table>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <div class="mb-4">
                    <i class="bi bi-stethoscope" style="font-size: 4rem; color: #dee2e6;"></i>
                </div>
                <h5 class="text-muted mb-3">No diagnoses yet</h5>
                <p class="text-muted mb-4">Start your first diagnosis to see your health insights here.</p>
                <a href="/health-oracle/diagnose.php" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>Start Your First Diagnosis
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Diagnosis Modal -->
<div class="modal fade" id="diagnosisModal" tabindex="-1" aria-labelledby="diagnosisModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                <h5 class="modal-title" id="diagnosisModalLabel">🩺 Diagnosis Details</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <h6 class="text-primary">📅 Date & Time:</h6>
                    <p id="modalDate" class="mb-3"></p>
                </div>
                <div class="mb-3">
                    <h6 class="text-primary">📝 Your Input:</h6>
                    <div id="modalInput" class="p-3 bg-light rounded mb-3"></div>
                </div>
                <div class="mb-3">
                    <h6 class="text-primary">🎯 AI Analysis:</h6>
                    <div id="modalResult" class="p-3 border rounded"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

<script>
function showDiagnosis(button) {
    // Get data from button attributes
    const input = button.getAttribute('data-input');
    const result = button.getAttribute('data-result');
    const date = button.getAttribute('data-date');

    // Decode HTML entities including &#039; and others
    function decodeHtml(html) {
        var txt = document.createElement("textarea");
        txt.innerHTML = html;
        var decoded = txt.value;
        // Fix specific entities that might not decode properly
        decoded = decoded.replace(/&#039;/g, "'");
        decoded = decoded.replace(/&quot;/g, '"');
        decoded = decoded.replace(/&amp;/g, '&');
        decoded = decoded.replace(/&lt;/g, '<');
        decoded = decoded.replace(/&gt;/g, '>');
        return decoded;
    }

    // Set modal content
    document.getElementById('modalDate').textContent = date;
    document.getElementById('modalInput').textContent = decodeHtml(input);
    document.getElementById('modalResult').innerHTML = decodeHtml(result);
}
</script>

</body>
</html>

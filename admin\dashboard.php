<?php
require_once __DIR__ . '/../includes/auth.php';
require_admin();
require_once __DIR__ . '/../includes/db.php';
require_once __DIR__ . '/../includes/functions.php';

// Site stats
$user_count = $pdo->query('SELECT COUNT(*) FROM users')->fetchColumn();
$diagnosis_count = $pdo->query('SELECT COUNT(*) FROM diagnoses')->fetchColumn();
$payment_count = $pdo->query('SELECT COUNT(*) FROM payments')->fetchColumn();

?><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Health Oracle</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&family=Inter:wght@400;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
    <style>
        body { font-family: 'Poppins', 'Inter', sans-serif; background: #F8F9FA; }
        .admin-card { border-radius: 1rem; box-shadow: 0 2px 16px rgba(70,130,180,0.08); background: #fff; }
    </style>
</head>
<body>
<?= getNavbarHtml() ?>
<div class="container py-5">
    <div class="admin-card p-4 mb-4">
        <h2 class="mb-3" style="color:#007BFF;">Admin Dashboard</h2>
        <div class="row g-3">
            <div class="col-md-4">
                <div class="p-3 bg-light rounded text-center">
                    <div class="h4 mb-0">Users</div>
                    <div class="display-6"><?= $user_count ?></div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="p-3 bg-light rounded text-center">
                    <div class="h4 mb-0">Diagnoses</div>
                    <div class="display-6"><?= $diagnosis_count ?></div>
                </div>
            </div>
    <div class="container mt-3"><?= renderConfigBanner() ?></div>

            <div class="col-md-4">
                <div class="p-3 bg-light rounded text-center">
                    <div class="h4 mb-0">Payments</div>
                    <div class="display-6"><?= $payment_count ?></div>
                </div>
            </div>
        </div>
        <div class="mt-4 d-flex gap-3">
            <a href="/health-oracle/admin/users.php" class="btn btn-primary">Users</a>
            <a href="/health-oracle/admin/transactions.php" class="btn btn-info text-white">Payments</a>
            <a href="/health-oracle/admin/settings.php" class="btn btn-outline-primary">Settings</a>
        </div>
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
require_once __DIR__ . '/../includes/auth.php';
require_admin();
require_once __DIR__ . '/../includes/db.php';
require_once __DIR__ . '/../includes/functions.php';

// Site stats
$user_count = $pdo->query('SELECT COUNT(*) FROM users')->fetchColumn();
$diagnosis_count = $pdo->query('SELECT COUNT(*) FROM diagnoses')->fetchColumn();
$payment_count = $pdo->query('SELECT COUNT(*) FROM payments')->fetchColumn();
$pending_payments = $pdo->query('SELECT COUNT(*) FROM payments WHERE status = "Pending"')->fetchColumn();
$active_subscriptions = $pdo->query('SELECT COUNT(*) FROM users WHERE subscription_plan != "free" AND subscription_expires >= CURDATE()')->fetchColumn();

// Recent activity
$recent_users = $pdo->query('SELECT name, email, date_registered FROM users ORDER BY date_registered DESC LIMIT 5')->fetchAll();
$recent_diagnoses = $pdo->query('SELECT d.*, u.name FROM diagnoses d LEFT JOIN users u ON d.user_id = u.id ORDER BY d.timestamp DESC LIMIT 5')->fetchAll();
$recent_payments = $pdo->query('SELECT p.*, u.name FROM payments p JOIN users u ON p.user_id = u.id ORDER BY p.timestamp DESC LIMIT 5')->fetchAll();

?><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Health Oracle</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="/assets/css/style.css">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        .admin-card {
            border-radius: 1.5rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            background: #fff;
            border: none;
            transition: transform 0.2s ease;
        }
        .admin-card:hover {
            transform: translateY(-2px);
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 1rem;
            border: none;
            position: relative;
            overflow: hidden;
        }
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            transform: translate(30px, -30px);
        }
        .stat-card-users { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .stat-card-diagnoses { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .stat-card-payments { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .stat-card-pending { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

        .activity-card {
            border-left: 4px solid #667eea;
        }
        .activity-item {
            border-bottom: 1px solid #e9ecef;
            padding: 1rem 0;
        }
        .activity-item:last-child {
            border-bottom: none;
        }
        .quick-action-btn {
            border-radius: 1rem;
            padding: 1rem;
            border: 2px dashed #dee2e6;
            transition: all 0.3s ease;
        }
        .quick-action-btn:hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }
    </style>
</head>
<body>
<?= getNavbarHtml() ?>
<div class="container py-5">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h2 mb-1" style="color:#495057;">
                            <i class="bi bi-speedometer2"></i> Admin Dashboard
                        </h1>
                        <p class="text-muted mb-0">Welcome back! Here's what's happening with Health Oracle today.</p>
                    </div>
                    <div class="text-end">
                        <div class="small text-muted">Last updated</div>
                        <div class="fw-semibold"><?= date('M d, Y H:i') ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Configuration Banner -->
    <div class="mb-4"><?= renderConfigBanner() ?></div>

    <!-- Stats Cards -->
    <div class="row g-4 mb-5">
        <div class="col-lg-3 col-md-6">
            <div class="stat-card stat-card-users p-4 text-center position-relative">
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <div>
                        <div class="h2 mb-0"><?= number_format($user_count) ?></div>
                        <div class="small opacity-75">Total Users</div>
                    </div>
                    <i class="bi bi-people-fill fs-1 opacity-25"></i>
                </div>
                <div class="small">
                    <i class="bi bi-arrow-up"></i> Active: <?= $active_subscriptions ?> subscribed
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="stat-card stat-card-diagnoses p-4 text-center position-relative">
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <div>
                        <div class="h2 mb-0"><?= number_format($diagnosis_count) ?></div>
                        <div class="small opacity-75">Diagnoses</div>
                    </div>
                    <i class="bi bi-activity fs-1 opacity-25"></i>
                </div>
                <div class="small">
                    <i class="bi bi-graph-up"></i> Total completed
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="stat-card stat-card-payments p-4 text-center position-relative">
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <div>
                        <div class="h2 mb-0"><?= number_format($payment_count) ?></div>
                        <div class="small opacity-75">Payments</div>
                    </div>
                    <i class="bi bi-credit-card fs-1 opacity-25"></i>
                </div>
                <div class="small">
                    <i class="bi bi-check-circle"></i> All transactions
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="stat-card stat-card-pending p-4 text-center position-relative">
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <div>
                        <div class="h2 mb-0"><?= number_format($pending_payments) ?></div>
                        <div class="small opacity-75">Pending</div>
                    </div>
                    <i class="bi bi-clock fs-1 opacity-25"></i>
                </div>
                <div class="small">
                    <?php if ($pending_payments > 0): ?>
                        <i class="bi bi-exclamation-circle"></i> Need approval
                    <?php else: ?>
                        <i class="bi bi-check-circle"></i> All caught up
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="admin-card p-4">
                <h5 class="mb-3"><i class="bi bi-lightning-charge"></i> Quick Actions</h5>
                <div class="row g-3">
                    <div class="col-lg-3 col-md-6">
                        <a href="/health-oracle/admin/users.php" class="btn quick-action-btn w-100 text-decoration-none position-relative">
                            <i class="bi bi-people fs-3 d-block mb-2 text-primary"></i>
                            <div class="fw-semibold">Manage Users</div>
                            <small class="text-muted">View and edit user accounts</small>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <a href="/health-oracle/admin/transactions.php" class="btn quick-action-btn w-100 text-decoration-none position-relative">
                            <i class="bi bi-credit-card fs-3 d-block mb-2 text-success"></i>
                            <div class="fw-semibold">Review Payments</div>
                            <small class="text-muted">Approve pending transactions</small>
                            <?php if ($pending_payments > 0): ?>
                                <span class="badge bg-danger position-absolute top-0 end-0 translate-middle"><?= $pending_payments ?></span>
                            <?php endif; ?>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <a href="/health-oracle/admin/settings.php" class="btn quick-action-btn w-100 text-decoration-none">
                            <i class="bi bi-gear fs-3 d-block mb-2 text-warning"></i>
                            <div class="fw-semibold">Settings</div>
                            <small class="text-muted">Configure system settings</small>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <a href="/health-oracle/diagnose.php" class="btn quick-action-btn w-100 text-decoration-none">
                            <i class="bi bi-stethoscope fs-3 d-block mb-2 text-info"></i>
                            <div class="fw-semibold">Test Diagnosis</div>
                            <small class="text-muted">Try the diagnosis tool</small>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row g-4">
        <div class="col-lg-4">
            <div class="admin-card activity-card p-4 h-100">
                <h6 class="mb-3"><i class="bi bi-person-plus"></i> Recent Users</h6>
                <?php if ($recent_users): ?>
                    <?php foreach ($recent_users as $user): ?>
                        <div class="activity-item">
                            <div class="fw-semibold"><?= htmlspecialchars($user['name']) ?></div>
                            <div class="small text-muted"><?= htmlspecialchars($user['email']) ?></div>
                            <div class="small text-muted"><?= date('M d, Y', strtotime($user['date_registered'])) ?></div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <p class="text-muted small">No recent users</p>
                <?php endif; ?>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="admin-card activity-card p-4 h-100">
                <h6 class="mb-3"><i class="bi bi-activity"></i> Recent Diagnoses</h6>
                <?php if ($recent_diagnoses): ?>
                    <?php foreach ($recent_diagnoses as $diagnosis): ?>
                        <div class="activity-item">
                            <div class="fw-semibold">
                                <?= $diagnosis['name'] ? htmlspecialchars($diagnosis['name']) : 'Guest User' ?>
                            </div>
                            <div class="small text-muted"><?= substr(htmlspecialchars($diagnosis['input_text']), 0, 50) ?>...</div>
                            <div class="small text-muted"><?= date('M d, H:i', strtotime($diagnosis['timestamp'])) ?></div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <p class="text-muted small">No recent diagnoses</p>
                <?php endif; ?>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="admin-card activity-card p-4 h-100">
                <h6 class="mb-3"><i class="bi bi-credit-card"></i> Recent Payments</h6>
                <?php if ($recent_payments): ?>
                    <?php foreach ($recent_payments as $payment): ?>
                        <div class="activity-item">
                            <div class="d-flex justify-content-between">
                                <div class="fw-semibold"><?= htmlspecialchars($payment['name']) ?></div>
                                <span class="badge bg-<?= $payment['status'] === 'Approved' ? 'success' : ($payment['status'] === 'Pending' ? 'warning' : 'danger') ?>">
                                    <?= $payment['status'] ?>
                                </span>
                            </div>
                            <div class="small text-muted"><?= $payment['plan_name'] ?> - $<?= $payment['amount'] ?></div>
                            <div class="small text-muted"><?= date('M d, H:i', strtotime($payment['timestamp'])) ?></div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <p class="text-muted small">No recent payments</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
require_once 'includes/auth.php';
require_once 'includes/db.php';

// Check if user is logged in
if (!is_logged_in()) {
    header('Location: /health-oracle/login.php');
    exit();
}

$user_id = $_SESSION['user_id'];
$error = '';

// Handle diagnosis deletion
if ($_POST && isset($_POST['delete_diagnosis'])) {
    $diagnosis_id = (int)$_POST['diagnosis_id'];
    $stmt = $pdo->prepare('DELETE FROM diagnoses WHERE id = ? AND user_id = ?');
    $stmt->execute([$diagnosis_id, $user_id]);
    header('Location: /health-oracle/history.php');
    exit();
}

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 10;
$offset = ($page - 1) * $per_page;

// Get total count
$total_stmt = $pdo->prepare('SELECT COUNT(*) FROM diagnoses WHERE user_id = ?');
$total_stmt->execute([$user_id]);
$total_diagnoses = $total_stmt->fetchColumn();
$total_pages = ceil($total_diagnoses / $per_page);

// Get diagnoses for current page
$diagnoses = $pdo->prepare('SELECT id, timestamp, input_text, text_summary, ai_response FROM diagnoses WHERE user_id = ? ORDER BY timestamp DESC LIMIT ? OFFSET ?');
$diagnoses->execute([$user_id, $per_page, $offset]);
$diagnoses = $diagnoses->fetchAll();

// Get user info
$user_stmt = $pdo->prepare('SELECT name FROM users WHERE id = ?');
$user_stmt->execute([$user_id]);
$user = $user_stmt->fetch();
$name = $user['name'] ?? 'User';
?>
<?php require_once 'includes/functions.php'; ?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnosis History - Health Oracle</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background: #F8F9FA;
            min-height: 100vh;
        }
        .history-card {
            border-radius: 1rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            background: #fff;
            margin-bottom: 2rem;
        }
        .btn-primary { background: #007BFF; border: none; }
        .btn-primary:hover { background: #1E90FF; }
        .table th { background-color: #f8f9fa; }
        .pagination .page-link { color: #007BFF; }
        .pagination .page-item.active .page-link { background-color: #007BFF; border-color: #007BFF; }
    </style>
</head>
<body>
<?= getNavbarHtml() ?>

<div class="container py-5">
    <div class="history-card p-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="mb-1" style="color:#007BFF;">📋 Diagnosis History</h2>
                <p class="text-muted">Complete history of your health consultations</p>
            </div>
            <a href="/health-oracle/dashboard.php" class="btn btn-outline-secondary">← Back to Dashboard</a>
        </div>

        <?php if ($diagnoses): ?>
            <div class="mb-3">
                <p class="text-muted">
                    Showing <?= count($diagnoses) ?> of <?= $total_diagnoses ?> diagnoses
                    (Page <?= $page ?> of <?= $total_pages ?>)
                </p>
            </div>
    <div class="container mt-3"><?= renderConfigBanner() ?></div>


            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead>
                        <tr>
                            <th width="15%">Date</th>
                            <th width="30%">Input</th>
                            <th width="40%">Summary</th>
                            <th width="15%">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($diagnoses as $d): ?>
                            <tr>
                                <td><?= date('M d, Y<br>H:i', strtotime($d['timestamp'])) ?></td>
                                <td><?= htmlspecialchars(substr($d['input_text'], 0, 100)) ?><?= strlen($d['input_text']) > 100 ? '...' : '' ?></td>
                                <td><?= $d['text_summary'] ? htmlspecialchars($d['text_summary']) : 'Processing...' ?></td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-outline-primary mb-1"
                                            data-bs-toggle="modal" data-bs-target="#diagnosisModal"
                                            data-input="<?= htmlspecialchars($d['input_text'], ENT_QUOTES) ?>"
                                            data-result="<?= htmlspecialchars($d['ai_response'], ENT_QUOTES) ?>"
                                            data-date="<?= date('M d, Y H:i', strtotime($d['timestamp'])) ?>"
                                            onclick="showDiagnosis(this)">
                                        👁️ View
                                    </button>
                                    <form method="post" style="display: inline;">
                                        <input type="hidden" name="diagnosis_id" value="<?= $d['id'] ?>">
                                        <button type="submit" name="delete_diagnosis" class="btn btn-sm btn-outline-danger"
                                                onclick="return confirm('Are you sure you want to delete this diagnosis?')">
                                            🗑️ Delete
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <nav aria-label="Diagnosis history pagination" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?= $page - 1 ?>">Previous</a>
                            </li>
                        <?php endif; ?>

                        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                            <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                                <a class="page-link" href="?page=<?= $i ?>"><?= $i ?></a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?= $page + 1 ?>">Next</a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>

        <?php else: ?>
            <div class="text-center py-5">
                <h4 class="text-muted mb-3">No diagnosis history found</h4>
                <p class="text-muted mb-4">You haven't performed any diagnoses yet.</p>
                <a href="/health-oracle/diagnose.php" class="btn btn-primary">Start Your First Diagnosis</a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Diagnosis Modal -->
<div class="modal fade" id="diagnosisModal" tabindex="-1" aria-labelledby="diagnosisModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header" style="background: #ADD8E6; color: #003366;">
                <h5 class="modal-title" id="diagnosisModalLabel">🩺 Diagnosis Details</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <h6 class="text-primary">📅 Date & Time:</h6>
                    <p id="modalDate" class="mb-3"></p>
                </div>
                <div class="mb-3">
                    <h6 class="text-primary">📝 Your Input:</h6>
                    <div id="modalInput" class="p-3 bg-light rounded mb-3"></div>
                </div>
                <div class="mb-3">
                    <h6 class="text-primary">🎯 AI Analysis:</h6>
                    <div id="modalResult" class="p-3 border rounded"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<script>
function showDiagnosis(button) {
    // Get data from button attributes
    const input = button.getAttribute('data-input');
    const result = button.getAttribute('data-result');
    const date = button.getAttribute('data-date');

    // Decode HTML entities including &#039; and others
    function decodeHtml(html) {
        var txt = document.createElement("textarea");
        txt.innerHTML = html;
        var decoded = txt.value;
        // Fix specific entities that might not decode properly
        decoded = decoded.replace(/&#039;/g, "'");
        decoded = decoded.replace(/&quot;/g, '"');
        decoded = decoded.replace(/&amp;/g, '&');
        decoded = decoded.replace(/&lt;/g, '<');
        decoded = decoded.replace(/&gt;/g, '>');
        return decoded;
    }

    // Set modal content
    document.getElementById('modalDate').textContent = date;
    document.getElementById('modalInput').textContent = decodeHtml(input);
    document.getElementById('modalResult').innerHTML = decodeHtml(result);
}
</script>
</body>
</html>

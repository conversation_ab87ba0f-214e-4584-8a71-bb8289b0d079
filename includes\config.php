<?php
// Lightweight .env loader (no external dependencies)
(function () {
    $envPath = dirname(__DIR__) . DIRECTORY_SEPARATOR . '.env';
    if (!file_exists($envPath)) return;
    $lines = file($envPath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        $line = trim($line);
        if ($line === '' || $line[0] === '#') continue;
        $pos = strpos($line, '=');
        if ($pos === false) continue;
        $name = trim(substr($line, 0, $pos));
        $value = trim(substr($line, $pos + 1));
        if ((strlen($value) >= 2) && (($value[0] === '"' && substr($value, -1) === '"') || ($value[0] === "'" && substr($value, -1) === "'"))) {
            $value = substr($value, 1, -1);
        }
        putenv($name . '=' . $value);
        $_ENV[$name] = $value;
        $_SERVER[$name] = $value;
    }
})();

// OpenAI API Configuration
// OpenAI API Configuration
// SECURITY: Read API key from environment; do not hardcode secrets in source control.
// On Windows/XAMPP, set in Apache envvars or system environment variables.
define('OPENAI_API_KEY', getenv('OPENAI_API_KEY') ?: '');
// Allow override via env; default to full gpt-4o
define('OPENAI_MODEL', getenv('OPENAI_MODEL') ?: 'gpt-4o');

define('MAX_REQUESTS_PER_IP_PER_DAY', 50);
define('ENABLE_LOGGING', true);

define('AI_PROVIDER', getenv('AI_PROVIDER') ?: 'openai'); // openai | hybrid
// RapidAPI disabled per request
define('RAPIDAPI_KEY', '');
define('RAPIDAPI_HOST', '');

define('APP_DEBUG', false);

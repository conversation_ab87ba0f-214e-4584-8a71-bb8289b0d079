<?php
require_once __DIR__ . '/includes/db.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/auth.php';

// Redirect if not a POST request with symptoms
if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_POST['symptoms'])) {
    header('Location: /health-oracle/guest-diagnose.php');
    exit();
}

$is_guest = isset($_POST['guest']);
$user_id = $is_guest ? null : ($_SESSION['user_id'] ?? null);
$ip = $_SERVER['REMOTE_ADDR'];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['symptoms'])) {
    $input = sanitize_input($_POST['symptoms']);
    if (!$input) {
        $error = 'Please enter your symptoms.';
    } else {
        // Usage limit enforcement
        if ($is_guest) {
            // Get guest limit from settings
            $limit = $pdo->query('SELECT guest_limit FROM settings WHERE id=1')->fetchColumn();
            if (!$limit) $limit = 3; // Default fallback
            $guest = $pdo->prepare('SELECT * FROM guests WHERE ip_address = ?');
            $guest->execute([$ip]);
            $guest = $guest->fetch();
            $usage_count = $guest ? $guest['usage_count'] : 0;
            if ($usage_count >= $limit) {
                $error = 'You have reached the daily guest usage limit. <a href="/health-oracle/register.php">Register</a> or <a href="/health-oracle/login.php">login</a> for more access.';
            } else {
                if ($guest) {
                    $pdo->prepare('UPDATE guests SET usage_count = usage_count + 1, last_usage_date = CURDATE() WHERE ip_address = ?')->execute([$ip]);
                } else {
                    $pdo->prepare('INSERT INTO guests (ip_address, usage_count, last_usage_date) VALUES (?, 1, CURDATE())')->execute([$ip]);
                }
            }
        } else {
            require_login();

            // Get user data including subscription info
            $user = $pdo->prepare('SELECT usage_count, last_usage_date, subscription_plan, subscription_expires FROM users WHERE id = ?');
            $user->execute([$user_id]);
            $user = $user->fetch();

            $today = date('Y-m-d');
            if ($user['last_usage_date'] !== $today) {
                $pdo->prepare('UPDATE users SET usage_count = 0, last_usage_date = ? WHERE id = ?')->execute([$today, $user_id]);
                $user['usage_count'] = 0;
            }

            // Check if subscription is active
            $has_active_subscription = false;
            if ($user['subscription_plan'] && $user['subscription_plan'] !== 'free') {
                if ($user['subscription_expires'] && $user['subscription_expires'] >= date('Y-m-d')) {
                    $has_active_subscription = true;
                }
            }

            // Set usage limits based on subscription
            $daily_limit = 5; // Default for free users
            if ($has_active_subscription) {
                if ($user['subscription_plan'] === 'basic') {
                    $daily_limit = 50; // Basic plan: 50 diagnoses per day
                } elseif ($user['subscription_plan'] === 'pro') {
                    $daily_limit = 999999; // Pro plan: unlimited (very high limit)
                }
            }

            if ($user['usage_count'] >= $daily_limit) {
                if ($has_active_subscription) {
                    $error = 'You have reached your daily limit. Your ' . ucfirst($user['subscription_plan']) . ' plan allows ' . $daily_limit . ' diagnoses per day.';
                } else {
                    $error = 'You have reached your daily usage limit. <a href="/health-oracle/invest.php">Subscribe</a> for unlimited access.';
                }
            } else {
                $pdo->prepare('UPDATE users SET usage_count = usage_count + 1, last_usage_date = ? WHERE id = ?')->execute([$today, $user_id]);
            }
        }
        // If no error, call AI API
        if (!isset($error)) {
            require_once __DIR__ . '/includes/config.php';

            $prompt = "You are a professional medical assistant. A user reports: \"$input\". Suggest possible illnesses and appropriate next steps in a clear, professional tone. Do not give emergency or life-threatening suggestions. Always recommend seeing a real doctor.";

            // Call OpenAI API
            try {
                $ai_response = callOpenAI($prompt);

                // Extract text summary for dashboard display
                $text_summary = extractTextSummary($ai_response);

                // Store in DB
                $stmt = $pdo->prepare('INSERT INTO diagnoses (user_id, guest_ip, input_text, ai_response, text_summary) VALUES (?, ?, ?, ?, ?)');
                $stmt->execute([$user_id, $is_guest ? $ip : null, $input, $ai_response, $text_summary]);
            } catch (Exception $e) {
                error_log("Diagnosis error: " . $e->getMessage());
                $error = "Sorry, we're experiencing technical difficulties. Please try again later.";
            }
        }
    }
}
?><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnosis Result - Health Oracle</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&family=Inter:wght@400;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
    <style>
        body { font-family: 'Poppins', 'Inter', sans-serif; background: #F8F9FA; }
        .result-card { max-width: 700px; margin: 3rem auto; border-radius: 1rem; box-shadow: 0 2px 16px rgba(70,130,180,0.08); background: #fff; }
        .btn-primary { background: #007BFF; border: none; }
        .btn-primary:hover { background: #1E90FF; }
    </style>
</head>
<body>
<div class="container">
    <div class="result-card p-4 mt-5">
        <h2 class="mb-4 text-center" style="color:#007BFF;">Diagnosis Result</h2>
        <?php if (isset($error)): ?>
            <div class="alert alert-danger"> <?= htmlspecialchars($error) ?> </div>
        <?php elseif (isset($ai_response)): ?>
            <div class="card mb-3 shadow-sm">
                <div class="card-body">
                    <h5 class="card-title" style="color:#4682B4;">AI Suggestions</h5>
                    <div class="card-text"> <?= $ai_response ?> </div>
                </div>
            </div>
            <div class="d-flex gap-2">
                <a href="javascript:window.print()" class="btn btn-outline-primary">Print</a>
                <a href="/health-oracle/dashboard.php" class="btn btn-primary">Back to Dashboard</a>
            </div>
        <?php else: ?>
            <div class="alert alert-info">No diagnosis submitted yet.</div>
        <?php endif; ?>
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
require_once __DIR__ . '/includes/auth.php';
require_login();
require_once __DIR__ . '/includes/db.php';
require_once __DIR__ . '/includes/functions.php';

$user_id = $_SESSION['user_id'];
$role = $_SESSION['role'];


// Redirect non-admins to profile (transactions now live there)
if ($role !== 'admin') {
    header('Location: /health-oracle/profile.php');
    exit();
}

if ($role === 'admin') {
    $stmt = $pdo->query('SELECT payments.*, users.name FROM payments JOIN users ON payments.user_id = users.id ORDER BY payments.timestamp DESC');
    $transactions = $stmt->fetchAll();
} else {
    $stmt = $pdo->prepare('SELECT * FROM payments WHERE user_id = ? ORDER BY timestamp DESC');
    $stmt->execute([$user_id]);
    $transactions = $stmt->fetchAll();
}
?><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transactions - Health Oracle</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&family=Inter:wght@400;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
    <style>
        body { font-family: 'Poppins', 'Inter', sans-serif; background: #F8F9FA; }
        .transactions-card { border-radius: 1rem; box-shadow: 0 2px 16px rgba(70,130,180,0.08); background: #fff; }
    </style>
</head>
<body>
<?= getNavbarHtml() ?>

<div class="container py-5">
    <div class="mt-3"><?= renderConfigBanner() ?></div>
    <div class="transactions-card p-4 mt-3">
        <h2 class="mb-4" style="color:#007BFF;">Transactions</h2>
        <?php if ($transactions): ?>
        <div class="table-responsive">
        <table class="table table-bordered">
            <thead>
                <tr>
                    <?php if ($role === 'admin'): ?><th>User</th><?php endif; ?>
                    <th>Plan</th><th>Amount</th><th>Reference</th><th>Method</th><th>Status</th><th>Date</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($transactions as $t): ?>
                <tr>
                    <?php if ($role === 'admin'): ?><td><?= htmlspecialchars($t['name']) ?></td><?php endif; ?>
                    <td><?= htmlspecialchars($t['plan_name']) ?></td>
                    <td><?= htmlspecialchars($t['amount']) ?></td>
                    <td><?= htmlspecialchars($t['reference_code']) ?></td>
                    <td><?= htmlspecialchars($t['payment_method']) ?></td>
                    <td><?= htmlspecialchars($t['status']) ?></td>
                    <td><?= htmlspecialchars($t['timestamp']) ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        </div>
        <?php else: ?>
            <p>No transactions found.</p>
        <?php endif; ?>
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
